// let destination = 'http://localhost:8888'
// let baseUrl_Po = 'http://localhost:8888'
function getEnvironment(url) {
    const regex = /ca-([a-z]+)-/;
    const match = url.match(regex);
    console.log(regex,match)
    return match ? match[1] : null;
  }
 
const host = window.location.host
const env = host.includes("localhost") ?  import.meta.env.MODE :getEnvironment(host)

let baseUrl_Notification = "https://cw-scp-notification-dev.cfapps.eu10-004.hana.ondemand.com"
let baseUrl_PR = "https://cw-scp-prmanagement-dev.cfapps.eu10-004.hana.ondemand.com"
let baseUrl_Messaging = "https://messaging.cherryworkproducts.com"
let baseUrl_CrudService = "https://crudservicesdev.cherryworkproducts.com"
let baseUrl_IWASCP = 'https://cw-scp-authentication-dev.cfapps.eu10-004.hana.ondemand.com'
let baseUrl_DocumentManagement = 'https://cw-mdg-documentmanagement.cfapps.eu10-004.hana.ondemand.com'
let baseUrl_ITMJava = 'https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com'
let baseUrl_MaterialManagement = 'https://cw-mdg-materialmanagement-dev.cfapps.eu10-004.hana.ondemand.com'
let baseUrl_Websocket = 'https://cw-mdg-notification.cfapps.eu10-004.hana.ondemand.com'
let baseUrl_AI = 'https://cw-mdg-artificialintelligence.cfapps.eu10-004.hana.ondemand.com'
let baseUrl_Admin = 'https://cw-mdg-admin-dev.cfapps.eu10-004.hana.ondemand.com'
// let baseUrl_ITMJava = "https://cw-mdg-iwm-qa.cfapps.eu10-004.hana.ondemand.com"
export {
    baseUrl_Notification,
    baseUrl_PR,
    baseUrl_Messaging,
    baseUrl_CrudService,
    baseUrl_IWASCP,
    baseUrl_DocumentManagement,
    baseUrl_MaterialManagement,
    baseUrl_ITMJava,
    baseUrl_Admin,
    baseUrl_Websocket,
    baseUrl_AI
}