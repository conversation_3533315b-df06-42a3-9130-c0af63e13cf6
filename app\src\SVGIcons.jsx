import { makeStyles } from "@mui/styles";
import { SvgIcon } from "@mui/material";
const useStyles = makeStyles((theme) => ({
  svgSize: {
    fontSize: 18,
  },
}));

export function ClipboardIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} viewBox="0 0 12 18">
      <path
        d="M9.46821 5.43145C9.51012 5.47325 9.54337 5.52291 9.56605 5.57758C9.58874 5.63225 9.60041 5.69086 9.60041 5.75005C9.60041 5.80924 9.58874 5.86785 9.56605 5.92252C9.54337 5.97719 9.51012 6.02685 9.46821 6.06865L7.89321 7.64365C7.81123 7.72554 7.70081 7.77264 7.58497 7.77516C7.46912 7.77767 7.35677 7.7354 7.27131 7.65715L6.59631 7.04065C6.51271 6.9591 6.46409 6.84825 6.46072 6.73151C6.45735 6.61477 6.49949 6.50129 6.57825 6.41506C6.65701 6.32882 6.7662 6.27658 6.88277 6.26938C6.99933 6.26217 7.11413 6.30057 7.20291 6.37645L7.56021 6.70225L8.83101 5.43145C8.87281 5.38954 8.92247 5.35629 8.97714 5.33361C9.03181 5.31092 9.09042 5.29925 9.14961 5.29925C9.2088 5.29925 9.26741 5.31092 9.32208 5.33361C9.37675 5.35629 9.42641 5.38954 9.46821 5.43145V5.43145ZM9.46821 10.5686C9.55271 10.4843 9.60023 10.3698 9.60031 10.2504C9.6004 10.131 9.55304 10.0164 9.46866 9.9319C9.38428 9.8474 9.26979 9.79988 9.15038 9.7998C9.03096 9.79971 8.91641 9.84707 8.83191 9.93145L7.56021 11.2022L7.20291 10.8764C7.11413 10.8006 6.99933 10.7622 6.88277 10.7694C6.7662 10.7766 6.65701 10.8288 6.57825 10.9151C6.49949 11.0013 6.45735 11.1148 6.46072 11.2315C6.46409 11.3482 6.51271 11.4591 6.59631 11.5406L7.27131 12.1572C7.35672 12.2351 7.46888 12.2772 7.58451 12.2747C7.70014 12.2722 7.81037 12.2253 7.89231 12.1437L9.46731 10.5686H9.46821ZM2.84961 6.20005C2.73026 6.20005 2.6158 6.24746 2.53141 6.33185C2.44702 6.41624 2.39961 6.5307 2.39961 6.65005C2.39961 6.7694 2.44702 6.88386 2.53141 6.96825C2.6158 7.05264 2.73026 7.10005 2.84961 7.10005H5.09961C5.21896 7.10005 5.33342 7.05264 5.41781 6.96825C5.5022 6.88386 5.54961 6.7694 5.54961 6.65005C5.54961 6.5307 5.5022 6.41624 5.41781 6.33185C5.33342 6.24746 5.21896 6.20005 5.09961 6.20005H2.84961ZM2.39961 11.15C2.39961 11.0307 2.44702 10.9162 2.53141 10.8319C2.6158 10.7475 2.73026 10.7 2.84961 10.7H5.09961C5.21896 10.7 5.33342 10.7475 5.41781 10.8319C5.5022 10.9162 5.54961 11.0307 5.54961 11.15C5.54961 11.2694 5.5022 11.3839 5.41781 11.4682C5.33342 11.5526 5.21896 11.6 5.09961 11.6H2.84961C2.73026 11.6 2.6158 11.5526 2.53141 11.4682C2.44702 11.3839 2.39961 11.2694 2.39961 11.15ZM3.37611 1.70005C3.46921 1.43673 3.64168 1.20876 3.86977 1.04757C4.09785 0.886384 4.37032 0.799903 4.64961 0.800049H7.34961C7.6289 0.799903 7.90137 0.886384 8.12945 1.04757C8.35754 1.20876 8.53001 1.43673 8.62311 1.70005H10.0496C10.4077 1.70005 10.751 1.84228 11.0042 2.09546C11.2574 2.34863 11.3996 2.69201 11.3996 3.05005V13.8501C11.3996 14.2081 11.2574 14.5515 11.0042 14.8046C10.751 15.0578 10.4077 15.2001 10.0496 15.2001H1.94961C1.59157 15.2001 1.24819 15.0578 0.995015 14.8046C0.741841 14.5515 0.599609 14.2081 0.599609 13.8501V3.05005C0.599609 2.69201 0.741841 2.34863 0.995015 2.09546C1.24819 1.84228 1.59157 1.70005 1.94961 1.70005H3.37611ZM4.64961 1.70005C4.53026 1.70005 4.4158 1.74746 4.33141 1.83185C4.24702 1.91624 4.19961 2.0307 4.19961 2.15005C4.19961 2.2694 4.24702 2.38386 4.33141 2.46825C4.4158 2.55264 4.53026 2.60005 4.64961 2.60005H7.34961C7.46896 2.60005 7.58342 2.55264 7.66781 2.46825C7.7522 2.38386 7.79961 2.2694 7.79961 2.15005C7.79961 2.0307 7.7522 1.91624 7.66781 1.83185C7.58342 1.74746 7.46896 1.70005 7.34961 1.70005H4.64961ZM3.37611 2.60005H1.94961C1.83026 2.60005 1.7158 2.64746 1.63141 2.73185C1.54702 2.81624 1.49961 2.9307 1.49961 3.05005V13.8501C1.49961 13.9694 1.54702 14.0839 1.63141 14.1682C1.7158 14.2526 1.83026 14.3001 1.94961 14.3001H10.0496C10.169 14.3001 10.2834 14.2526 10.3678 14.1682C10.4522 14.0839 10.4996 13.9694 10.4996 13.8501V3.05005C10.4996 2.9307 10.4522 2.81624 10.3678 2.73185C10.2834 2.64746 10.169 2.60005 10.0496 2.60005H8.62311C8.53001 2.86337 8.35754 3.09133 8.12945 3.25252C7.90137 3.41371 7.6289 3.5002 7.34961 3.50005H4.64961C4.37032 3.5002 4.09785 3.41371 3.86977 3.25252C3.64168 3.09133 3.46921 2.86337 3.37611 2.60005V2.60005Z"
        fill="#1D1D1D"
      />
    </SvgIcon>
  );
}
export function ViewFilters(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} viewBox="0 0 16 19" classes={{ root: classes.svgSize }}>
      <path
        d="M7.11521 16.7598C7.11521 17.0996 6.82994 17.375 6.47795 17.375H3.67403C2.26845 17.375 1.125 16.2711 1.125 14.9141L1.125 4.08594C1.125 2.72894 2.26845 1.625 3.67403 1.625L11.5088 1.625C12.9142 1.625 14.0578 2.72894 14.0578 4.08594V9.96143C14.0578 10.3012 13.7724 10.5767 13.4206 10.5767C13.0686 10.5767 12.7833 10.3012 12.7833 9.96143V4.08594C12.7833 3.4075 12.2115 2.85547 11.5088 2.85547L3.67403 2.85547C2.9713 2.85547 2.39951 3.4075 2.39951 4.08594L2.39951 14.9141C2.39951 15.5925 2.9713 16.1445 3.67403 16.1445H6.47795C6.82994 16.1445 7.11521 16.4199 7.11521 16.7598ZM10.8715 5.31641H4.3078C3.95581 5.31641 3.67054 5.59182 3.67054 5.93164C3.67054 6.27146 3.95581 6.54688 4.3078 6.54688H10.8715C11.2234 6.54688 11.5088 6.27146 11.5088 5.93164C11.5088 5.59182 11.2234 5.31641 10.8715 5.31641ZM11.5088 8.39258C11.5088 8.05276 11.2234 7.77734 10.8715 7.77734H4.3078C3.95581 7.77734 3.67054 8.05276 3.67054 8.39258C3.67054 8.7324 3.95581 9.00781 4.3078 9.00781H10.8715C11.2234 9.00781 11.5088 8.7324 11.5088 8.39258ZM4.3078 10.2383C3.95581 10.2383 3.67054 10.5137 3.67054 10.8535C3.67054 11.1933 3.95581 11.4688 4.3078 11.4688H7.65688C8.00886 11.4688 8.29413 11.1933 8.29413 10.8535C8.29413 10.5137 8.00886 10.2383 7.65688 10.2383H4.3078ZM15.6342 14.7448C15.6118 14.7756 15.535 14.8805 15.4873 14.9386C15.2738 15.1978 14.7742 15.8045 14.0791 16.34C13.1878 17.0268 12.2706 17.375 11.353 17.375C10.4353 17.375 9.51811 17.0268 8.62683 16.34C7.93169 15.8045 7.43209 15.1976 7.21876 14.9386C7.17097 14.8805 7.09417 14.7755 7.07177 14.7448C6.91731 14.5325 6.91731 14.2496 7.07177 14.0374C7.09417 14.0066 7.17097 13.9016 7.21876 13.8435C7.43209 13.5845 7.93169 12.9778 8.62683 12.4422C9.51811 11.7555 10.4353 11.4072 11.353 11.4072C12.2706 11.4072 13.1878 11.7555 14.0791 12.4422C14.7742 12.9778 15.2738 13.5846 15.4872 13.8437C15.535 13.9017 15.6118 14.0067 15.6342 14.0375C15.7886 14.2497 15.7886 14.5325 15.6342 14.7448ZM14.3049 14.3911C13.2916 13.2273 12.2995 12.6377 11.353 12.6377C10.4065 12.6377 9.41431 13.2272 8.40105 14.3911C9.41431 15.5549 10.4064 16.1445 11.353 16.1445C12.2995 16.1445 13.2916 15.555 14.3049 14.3911ZM11.3848 13.2222C10.7161 13.2222 10.174 13.7455 10.174 14.3911C10.174 15.0367 10.7161 15.5601 11.3848 15.5601C12.0536 15.5601 12.5956 15.0367 12.5956 14.3911C12.5956 13.7455 12.0536 13.2222 11.3848 13.2222Z"
        fill="#1D1D1D"
      />
    </SvgIcon>
  );
}

export function ForwardIcon(props) {
  return (
    <svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#1D1D11" {...props}>
      <path d="M11 0.75L14.75 4.5L11 8.25" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M14.75 4.5H8.75C4.60775 4.5 1.25 7.85775 1.25 12V12.75" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  );
}
export function PriorityIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 21C13.1046 21 14 20.1046 14 19C14 17.8954 13.1046 17 12 17C10.8954 17 10 17.8954 10 19C10 20.1046 10.8954 21 12 21Z" fill="#DA2C2C" />
      <path d="M12 3C10.9 3 10 3.9 10 5V13C10 14.1 10.9 15 12 15C13.1 15 14 14.1 14 13V5C14 3.9 13.1 3 12 3Z" fill="#DA2C2C" />
    </SvgIcon>
  );
}

export function AcceptIcon(props) {
  return (
    <SvgIcon width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M5.25006 9.44993L2.80007 6.99993L1.9834 7.8166L5.25006 11.0833L12.2501 4.08327L11.4334 3.2666L5.25006 9.44993Z" fill="#212121" />
    </SvgIcon>
  );
}

export function RejectIcon(props) {
  return (
    <SvgIcon width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.6749 3.33076C10.4474 3.10326 10.0799 3.10326 9.85242 3.33076L6.99992 6.17742L4.14742 3.32492C3.91992 3.09742 3.55242 3.09742 3.32492 3.32492C3.09742 3.55242 3.09742 3.91992 3.32492 4.14742L6.17742 6.99992L3.32492 9.85242C3.09742 10.0799 3.09742 10.4474 3.32492 10.6749C3.55242 10.9024 3.91992 10.9024 4.14742 10.6749L6.99992 7.82242L9.85242 10.6749C10.0799 10.9024 10.4474 10.9024 10.6749 10.6749C10.9024 10.4474 10.9024 10.0799 10.6749 9.85242L7.82242 6.99992L10.6749 4.14742C10.8966 3.92576 10.8966 3.55242 10.6749 3.33076Z" fill="#323232" />
    </SvgIcon>
  );
}

export function ChatIcon(props) {
  return (
    <SvgIcon width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.667 1.1665H2.33366C1.69199 1.1665 1.16699 1.6915 1.16699 2.33317V12.8332L3.50033 10.4998H11.667C12.3087 10.4998 12.8337 9.97484 12.8337 9.33317V2.33317C12.8337 1.6915 12.3087 1.1665 11.667 1.1665ZM11.667 9.33317H3.50033L2.33366 10.4998V2.33317H11.667V9.33317Z" fill="#323232" />
    </SvgIcon>
  );
}

export function FilterIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.99969 6H16.9997L11.9897 12.3L6.99969 6ZM4.24969 5.61C6.26969 8.2 9.99969 13 9.99969 13V19C9.99969 19.55 10.4497 20 10.9997 20H12.9997C13.5497 20 13.9997 19.55 13.9997 19V13C13.9997 13 17.7197 8.2 19.7397 5.61C20.2497 4.95 19.7797 4 18.9497 4H5.03969C4.20969 4 3.73969 4.95 4.24969 5.61Z" fill="#323232" />
    </SvgIcon>
  );
}

export function BoardViewIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3 6.75C3 5.75544 3.39509 4.80161 4.09835 4.09835C4.80161 3.39509 5.75544 3 6.75 3H17.25C18.2446 3 19.1984 3.39509 19.9017 4.09835C20.6049 4.80161 21 5.75544 21 6.75V17.25C21 18.2446 20.6049 19.1984 19.9017 19.9017C19.1984 20.6049 18.2446 21 17.25 21H6.75C5.75544 21 4.80161 20.6049 4.09835 19.9017C3.39509 19.1984 3 18.2446 3 17.25V6.75ZM6.75 4.5C6.15326 4.5 5.58097 4.73705 5.15901 5.15901C4.73705 5.58097 4.5 6.15326 4.5 6.75V7.5H11.25V4.5H6.75ZM12.75 4.5V15H19.5V6.75C19.5 6.15326 19.2629 5.58097 18.841 5.15901C18.419 4.73705 17.8467 4.5 17.25 4.5H12.75ZM19.5 16.5H12.75V19.5H17.25C17.8467 19.5 18.419 19.2629 18.841 18.841C19.2629 18.419 19.5 17.8467 19.5 17.25V16.5ZM11.25 19.5V9H4.5V17.25C4.5 17.8467 4.73705 18.419 5.15901 18.841C5.58097 19.2629 6.15326 19.5 6.75 19.5H11.25Z"
        fill="#212121"
      />
    </SvgIcon>
  );
}

export function MapViewIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M1.125 2.8125C1.125 2.36495 1.30279 1.93572 1.61926 1.61926C1.93572 1.30279 2.36495 1.125 2.8125 1.125H6.1875C6.63505 1.125 7.06428 1.30279 7.38074 1.61926C7.69721 1.93572 7.875 2.36495 7.875 2.8125V6.1875C7.875 6.63505 7.69721 7.06428 7.38074 7.38074C7.06428 7.69721 6.63505 7.875 6.1875 7.875H2.8125C2.36495 7.875 1.93572 7.69721 1.61926 7.38074C1.30279 7.06428 1.125 6.63505 1.125 6.1875V2.8125ZM2.8125 2.25C2.66332 2.25 2.52024 2.30926 2.41475 2.41475C2.30926 2.52024 2.25 2.66332 2.25 2.8125V6.1875C2.25 6.33668 2.30926 6.47976 2.41475 6.58525C2.52024 6.69074 2.66332 6.75 2.8125 6.75H6.1875C6.33668 6.75 6.47976 6.69074 6.58525 6.58525C6.69074 6.47976 6.75 6.33668 6.75 6.1875V2.8125C6.75 2.66332 6.69074 2.52024 6.58525 2.41475C6.47976 2.30926 6.33668 2.25 6.1875 2.25H2.8125ZM10.125 2.8125C10.125 2.36495 10.3028 1.93572 10.6193 1.61926C10.9357 1.30279 11.3649 1.125 11.8125 1.125H15.1875C15.6351 1.125 16.0643 1.30279 16.3807 1.61926C16.6972 1.93572 16.875 2.36495 16.875 2.8125V6.1875C16.875 6.63505 16.6972 7.06428 16.3807 7.38074C16.0643 7.69721 15.6351 7.875 15.1875 7.875H11.8125C11.3649 7.875 10.9357 7.69721 10.6193 7.38074C10.3028 7.06428 10.125 6.63505 10.125 6.1875V2.8125ZM11.8125 2.25C11.6633 2.25 11.5202 2.30926 11.4148 2.41475C11.3093 2.52024 11.25 2.66332 11.25 2.8125V6.1875C11.25 6.33668 11.3093 6.47976 11.4148 6.58525C11.5202 6.69074 11.6633 6.75 11.8125 6.75H15.1875C15.3367 6.75 15.4798 6.69074 15.5852 6.58525C15.6907 6.47976 15.75 6.33668 15.75 6.1875V2.8125C15.75 2.66332 15.6907 2.52024 15.5852 2.41475C15.4798 2.30926 15.3367 2.25 15.1875 2.25H11.8125ZM1.125 11.8125C1.125 11.3649 1.30279 10.9357 1.61926 10.6193C1.93572 10.3028 2.36495 10.125 2.8125 10.125H6.1875C6.63505 10.125 7.06428 10.3028 7.38074 10.6193C7.69721 10.9357 7.875 11.3649 7.875 11.8125V15.1875C7.875 15.6351 7.69721 16.0643 7.38074 16.3807C7.06428 16.6972 6.63505 16.875 6.1875 16.875H2.8125C2.36495 16.875 1.93572 16.6972 1.61926 16.3807C1.30279 16.0643 1.125 15.6351 1.125 15.1875V11.8125ZM2.8125 11.25C2.66332 11.25 2.52024 11.3093 2.41475 11.4148C2.30926 11.5202 2.25 11.6633 2.25 11.8125V15.1875C2.25 15.3367 2.30926 15.4798 2.41475 15.5852C2.52024 15.6907 2.66332 15.75 2.8125 15.75H6.1875C6.33668 15.75 6.47976 15.6907 6.58525 15.5852C6.69074 15.4798 6.75 15.3367 6.75 15.1875V11.8125C6.75 11.6633 6.69074 11.5202 6.58525 11.4148C6.47976 11.3093 6.33668 11.25 6.1875 11.25H2.8125ZM10.125 11.8125C10.125 11.3649 10.3028 10.9357 10.6193 10.6193C10.9357 10.3028 11.3649 10.125 11.8125 10.125H15.1875C15.6351 10.125 16.0643 10.3028 16.3807 10.6193C16.6972 10.9357 16.875 11.3649 16.875 11.8125V15.1875C16.875 15.6351 16.6972 16.0643 16.3807 16.3807C16.0643 16.6972 15.6351 16.875 15.1875 16.875H11.8125C11.3649 16.875 10.9357 16.6972 10.6193 16.3807C10.3028 16.0643 10.125 15.6351 10.125 15.1875V11.8125ZM11.8125 11.25C11.6633 11.25 11.5202 11.3093 11.4148 11.4148C11.3093 11.5202 11.25 11.6633 11.25 11.8125V15.1875C11.25 15.3367 11.3093 15.4798 11.4148 15.5852C11.5202 15.6907 11.6633 15.75 11.8125 15.75H15.1875C15.3367 15.75 15.4798 15.6907 15.5852 15.5852C15.6907 15.4798 15.75 15.3367 15.75 15.1875V11.8125C15.75 11.6633 15.6907 11.5202 15.5852 11.4148C15.4798 11.3093 15.3367 11.25 15.1875 11.25H11.8125Z"
        fill="black"
      />
    </SvgIcon>
  );
}

export function ListViewIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M2.25 2.8125C2.10082 2.8125 1.95774 2.87176 1.85225 2.97725C1.74676 3.08274 1.6875 3.22582 1.6875 3.375V4.5C1.6875 4.64918 1.74676 4.79226 1.85225 4.89775C1.95774 5.00324 2.10082 5.0625 2.25 5.0625H3.375C3.52418 5.0625 3.66726 5.00324 3.77275 4.89775C3.87824 4.79226 3.9375 4.64918 3.9375 4.5V3.375C3.9375 3.22582 3.87824 3.08274 3.77275 2.97725C3.66726 2.87176 3.52418 2.8125 3.375 2.8125H2.25ZM3.375 3.375H2.25V4.5H3.375V3.375Z" fill="#1E1A1A" />
      <path
        d="M5.625 3.9375C5.625 3.78832 5.68426 3.64524 5.78975 3.53975C5.89524 3.43426 6.03832 3.375 6.1875 3.375H16.3125C16.4617 3.375 16.6048 3.43426 16.7102 3.53975C16.8157 3.64524 16.875 3.78832 16.875 3.9375C16.875 4.08668 16.8157 4.22976 16.7102 4.33525C16.6048 4.44074 16.4617 4.5 16.3125 4.5H6.1875C6.03832 4.5 5.89524 4.44074 5.78975 4.33525C5.68426 4.22976 5.625 4.08668 5.625 3.9375ZM6.1875 7.875C6.03832 7.875 5.89524 7.93426 5.78975 8.03975C5.68426 8.14524 5.625 8.28832 5.625 8.4375C5.625 8.58668 5.68426 8.72976 5.78975 8.83525C5.89524 8.94074 6.03832 9 6.1875 9H16.3125C16.4617 9 16.6048 8.94074 16.7102 8.83525C16.8157 8.72976 16.875 8.58668 16.875 8.4375C16.875 8.28832 16.8157 8.14524 16.7102 8.03975C16.6048 7.93426 16.4617 7.875 16.3125 7.875H6.1875ZM6.1875 12.375C6.03832 12.375 5.89524 12.4343 5.78975 12.5398C5.68426 12.6452 5.625 12.7883 5.625 12.9375C5.625 13.0867 5.68426 13.2298 5.78975 13.3352C5.89524 13.4407 6.03832 13.5 6.1875 13.5H16.3125C16.4617 13.5 16.6048 13.4407 16.7102 13.3352C16.8157 13.2298 16.875 13.0867 16.875 12.9375C16.875 12.7883 16.8157 12.6452 16.7102 12.5398C16.6048 12.4343 16.4617 12.375 16.3125 12.375H6.1875Z"
        fill="#1E1A1A"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M1.6875 7.875C1.6875 7.72582 1.74676 7.58274 1.85225 7.47725C1.95774 7.37176 2.10082 7.3125 2.25 7.3125H3.375C3.52418 7.3125 3.66726 7.37176 3.77275 7.47725C3.87824 7.58274 3.9375 7.72582 3.9375 7.875V9C3.9375 9.14918 3.87824 9.29226 3.77275 9.39775C3.66726 9.50324 3.52418 9.5625 3.375 9.5625H2.25C2.10082 9.5625 1.95774 9.50324 1.85225 9.39775C1.74676 9.29226 1.6875 9.14918 1.6875 9V7.875ZM2.25 7.875H3.375V9H2.25V7.875ZM2.25 11.8125C2.10082 11.8125 1.95774 11.8718 1.85225 11.9773C1.74676 12.0827 1.6875 12.2258 1.6875 12.375V13.5C1.6875 13.6492 1.74676 13.7923 1.85225 13.8977C1.95774 14.0032 2.10082 14.0625 2.25 14.0625H3.375C3.52418 14.0625 3.66726 14.0032 3.77275 13.8977C3.87824 13.7923 3.9375 13.6492 3.9375 13.5V12.375C3.9375 12.2258 3.87824 12.0827 3.77275 11.9773C3.66726 11.8718 3.52418 11.8125 3.375 11.8125H2.25ZM3.375 12.375H2.25V13.5H3.375V12.375Z"
        fill="#1E1A1A"
      />
    </SvgIcon>
  );
}
export function AlarmSnooze(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.963 2.78026L14.022 1.71826L16.2795 3.96826L15.2198 5.03101L12.963 2.78026ZM5.02353 2.78026L2.78103 5.02951L1.71753 3.97051L3.96003 1.72051L5.02353 2.78026ZM9.00003 3.00001C5.34153 3.00001 2.25003 6.09076 2.25003 9.75001C2.25003 13.4093 5.34153 16.5 9.00003 16.5C12.6593 16.5 15.75 13.4093 15.75 9.75001C15.75 6.09076 12.6593 3.00001 9.00003 3.00001ZM12 12.75H6.09828L9.09828 8.25001H6.00003V6.75001H11.901L11.1248 7.91551L11.124 7.91626L8.90178 11.25H12V12.75Z" fill="black" />
    </SvgIcon>
  );
}
export function AlarmAdd(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M15.885 3.75748L13.575 1.83748C13.26 1.57498 12.7875 1.61248 12.5175 1.93498C12.255 2.24998 12.3 2.72248 12.615 2.99248L14.9175 4.91248C15.2325 5.17498 15.705 5.13748 15.975 4.81498C16.245 4.49998 16.2 4.02748 15.885 3.75748V3.75748ZM3.075 4.91248L5.3775 2.99248C5.7 2.72248 5.745 2.24998 5.475 1.93498C5.2125 1.61248 4.74 1.57498 4.425 1.83748L2.115 3.75748C1.8 4.02748 1.755 4.49998 2.025 4.81498C2.2875 5.13748 2.76 5.17498 3.075 4.91248ZM9 2.99998C5.2725 2.99998 2.25 6.02248 2.25 9.74998C2.25 13.4775 5.2725 16.5 9 16.5C12.7275 16.5 15.75 13.4775 15.75 9.74998C15.75 6.02248 12.7275 2.99998 9 2.99998ZM9 15C6.105 15 3.75 12.645 3.75 9.74998C3.75 6.85498 6.105 4.49998 9 4.49998C11.895 4.49998 14.25 6.85498 14.25 9.74998C14.25 12.645 11.895 15 9 15ZM11.25 8.99998H9.75V7.49998C9.75 7.08748 9.4125 6.74998 9 6.74998C8.5875 6.74998 8.25 7.08748 8.25 7.49998V8.99998H6.75C6.3375 8.99998 6 9.33748 6 9.74998C6 10.1625 6.3375 10.5 6.75 10.5H8.25V12C8.25 12.4125 8.5875 12.75 9 12.75C9.4125 12.75 9.75 12.4125 9.75 12V10.5H11.25C11.6625 10.5 12 10.1625 12 9.74998C12 9.33748 11.6625 8.99998 11.25 8.99998Z"
        fill="#1D1D11"
      />
    </SvgIcon>
  );
}
export function SettingsIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_19261_141752)">
        <path d="M15.0001 11.4806L17.4826 8.99813L15.0001 6.51562V2.99813H11.4826L9.00008 0.515625L6.51758 2.99813H3.00008V6.51562L0.517578 8.99813L3.00008 11.4806V14.9981H6.51758L9.00008 17.4806L11.4826 14.9981H15.0001V11.4806ZM9.00008 13.4981C6.51758 13.4981 4.50008 11.4806 4.50008 8.99813C4.50008 6.51563 6.51758 4.49813 9.00008 4.49813C11.4826 4.49813 13.5001 6.51563 13.5001 8.99813C13.5001 11.4806 11.4826 13.4981 9.00008 13.4981Z" fill="#323232" />
      </g>
      <defs>
        <clipPath id="clip0_19261_141752">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </SvgIcon>
  );
}
export function CalenderIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 2.25H14.25V0.75H12.75V2.25H5.25V0.75H3.75V2.25H3C2.175 2.25 1.5 2.925 1.5 3.75V15.75C1.5 16.575 2.175 17.25 3 17.25H15C15.825 17.25 16.5 16.575 16.5 15.75V3.75C16.5 2.925 15.825 2.25 15 2.25ZM15 15.75H3V7.5H15V15.75ZM15 6H3V3.75H15V6Z" fill="#323232" />
    </SvgIcon>
  );
}
export function Clock(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} MuiSvgIcon-fontSizeSmall width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 0C2.69147 0 0 2.69147 0 6C0 9.30853 2.69147 12 6 12C9.30853 12 12 9.30853 12 6C12 2.69147 9.30853 0 6 0ZM8.85352 9.10345C8.75601 9.20096 8.62802 9.25003 8.50003 9.25003C8.37204 9.25003 8.24396 9.20096 8.14655 9.10345L5.64651 6.60352C5.55249 6.51004 5.50003 6.38297 5.50003 6.25003V3C5.50003 2.72351 5.72397 2.50003 6 2.50003C6.27603 2.50003 6.49997 2.72351 6.49997 3V6.04303L8.85352 8.39648C9.04898 8.59204 9.04898 8.90799 8.85352 9.10345Z" fill="#282828" />
    </SvgIcon>
  );
}
export function PlusIcon(props) {
  return (
    <svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.293 4.08008V6.53125H0.546875V4.08008H10.293ZM6.75781 0.222656V10.5742H4.0918V0.222656H6.75781Z" fill="#00518D" />
    </svg>
  );
}
export function ConnectorConfigIcon(props) {
  return (
    <svg width="108" height="108" viewBox="0 0 108 108" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity="0.4">
        <path
          d="M80.9995 71.998L98.9995 53.998L80.9995 35.998V49.498H58.2295C56.6995 35.548 48.0595 23.758 35.9995 17.773C35.8195 10.393 29.8795 4.49805 22.4995 4.49805C15.0295 4.49805 8.99951 10.528 8.99951 17.998C8.99951 25.468 15.0295 31.498 22.4995 31.498C26.7745 31.498 30.5095 29.473 32.9845 26.368C41.5345 31.048 47.6995 39.463 49.1395 49.498H35.1895C33.2995 44.278 28.3495 40.498 22.4995 40.498C15.0295 40.498 8.99951 46.528 8.99951 53.998C8.99951 61.468 15.0295 67.498 22.4995 67.498C28.3495 67.498 33.2995 63.718 35.1895 58.498H49.1395C47.6995 68.533 41.5345 76.948 33.0295 81.628C30.5095 78.523 26.7745 76.498 22.4995 76.498C15.0295 76.498 8.99951 82.528 8.99951 89.998C8.99951 97.468 15.0295 103.498 22.4995 103.498C29.8795 103.498 35.8195 97.603 35.9545 90.223C48.0145 84.238 56.6545 72.448 58.1845 58.498H80.9995V71.998Z"
          fill="#9A9A9A"
        />
      </g>
    </svg>
  );
}

export const CancelIcon = (props) => {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.9797 6.46697L11.8397 1.60697C11.9489 1.47944 12.006 1.31539 11.9995 1.1476C11.993 0.979818 11.9235 0.820659 11.8047 0.701929C11.686 0.583199 11.5268 0.513644 11.3591 0.507163C11.1913 0.500683 11.0272 0.557754 10.8997 0.666971L6.0397 5.52697L1.1797 0.660304C1.05216 0.551087 0.888111 0.494016 0.720327 0.500497C0.552543 0.506978 0.393384 0.576533 0.274654 0.695262C0.155924 0.813992 0.086369 0.973151 0.0798883 1.14094C0.0734075 1.30872 0.130479 1.47277 0.239696 1.6003L5.0997 6.46697L0.233029 11.327C0.163241 11.3867 0.106561 11.4603 0.0665457 11.543C0.0265302 11.6257 0.00404312 11.7158 0.000496786 11.8076C-0.00304954 11.8994 0.0124211 11.991 0.0459375 12.0765C0.0794538 12.1621 0.130293 12.2398 0.195263 12.3047C0.260232 12.3697 0.337931 12.4205 0.423481 12.4541C0.509031 12.4876 0.600586 12.503 0.692398 12.4995C0.784211 12.496 0.874299 12.4735 0.957009 12.4335C1.03972 12.3934 1.11326 12.3368 1.17303 12.267L6.0397 7.40697L10.8997 12.267C11.0272 12.3762 11.1913 12.4333 11.3591 12.4268C11.5268 12.4203 11.686 12.3507 11.8047 12.232C11.9235 12.1133 11.993 11.9541 11.9995 11.7863C12.006 11.6186 11.9489 11.4545 11.8397 11.327L6.9797 6.46697Z"
        fill="#1D1D11"
      />
    </SvgIcon>
  );
};
export const CursorIcon = (props) => {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="19" height="24" viewBox="0 0 19 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        style={props?.style}
        d="M18.267 11.7352C17.067 10.4685 15.467 9.60188 13.8004 9.26855C13.067 9.06855 12.3337 8.93522 11.6004 8.86855C13.467 6.66855 13.1337 3.33522 10.9337 1.46855C8.7337 -0.398117 5.40037 -0.064784 3.5337 2.13522C1.66704 4.33522 2.00037 7.66855 4.20037 9.53522C4.60037 9.86855 5.00037 10.1352 5.40037 10.2685V11.7352L4.3337 10.7352C3.40037 9.80188 1.86704 9.80188 0.867038 10.7352C-0.0662952 11.6685 -0.132962 13.1352 0.800371 14.0685L3.86704 17.6685C4.00037 18.6019 4.3337 19.4685 4.80037 20.2685C5.1337 20.8685 5.60037 21.4685 6.06704 21.9352V23.2019C6.06704 23.6019 6.33371 23.8685 6.73371 23.8685H15.8004C16.1337 23.8685 16.467 23.5352 16.467 23.2019V21.4685C17.7337 19.9352 18.4004 18.0019 18.4004 16.0685V12.2019C18.467 11.9352 18.4004 11.8019 18.267 11.7352V11.7352ZM3.60037 5.46855C3.60037 3.26855 5.40037 1.53522 7.60037 1.60188C9.80037 1.60188 11.5337 3.40188 11.467 5.60188C11.467 6.80188 10.9337 7.86855 10.0004 8.60188V5.26855C9.96633 4.6908 9.71257 4.14795 9.29112 3.75129C8.86967 3.35463 8.31246 3.13421 7.73371 3.13522C6.53371 3.06855 5.46704 4.06855 5.46704 5.26855V8.73522C4.3337 8.06855 3.66704 6.80188 3.60037 5.46855ZM17.1337 16.0019C17.2004 17.7352 16.6004 19.4019 15.467 20.7352C15.3337 20.8685 15.2004 21.0019 15.2004 21.2019V22.6019H7.46704V21.6685C7.46704 21.4685 7.3337 21.2685 7.20037 21.1352C6.7337 20.7352 6.3337 20.2685 6.00037 19.6685C5.60037 19.0019 5.3337 18.2019 5.20037 17.4019C5.20037 17.2685 5.13371 17.1352 5.06704 17.0019L1.86704 13.2019C1.66704 13.0019 1.5337 12.7352 1.5337 12.4019C1.5337 12.1352 1.66704 11.8019 1.86704 11.6019C2.3337 11.2019 3.00037 11.2019 3.46704 11.6019L5.40037 13.5352V15.5352L6.66704 14.8685V5.26855C6.7337 4.80188 7.1337 4.40188 7.66704 4.46855C8.1337 4.46855 8.60037 4.80188 8.60037 5.26855V12.9352L9.9337 13.2019V10.1352C10.0004 10.0685 10.067 10.0685 10.1337 10.0019C10.6004 10.0019 11.067 10.0685 11.5337 10.1352V13.5352L12.6004 13.7352V10.2685L13.4004 10.4685C13.7337 10.5352 14.067 10.6685 14.4004 10.8019V14.1352L15.467 14.3352V11.2685C16.067 11.5352 16.6004 11.9352 17.067 12.4019L17.1337 16.0019V16.0019Z"
        fill="#3026B9"
      />
    </SvgIcon>
  );
};
export const EosIcon = (props) => {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        style={props?.style}
        d="M17.4997 0.667969H2.49967C2.05765 0.667969 1.63372 0.843563 1.32116 1.15612C1.0086 1.46868 0.833008 1.89261 0.833008 2.33464V12.3346C0.833008 12.7767 1.0086 13.2006 1.32116 13.5131C1.63372 13.8257 2.05765 14.0013 2.49967 14.0013H8.33301V15.668H6.66634V17.3346H13.333V15.668H11.6663V14.0013H17.4997C17.9417 14.0013 18.3656 13.8257 18.6782 13.5131C18.9907 13.2006 19.1663 12.7767 19.1663 12.3346V2.33464C19.1663 1.89261 18.9907 1.46868 18.6782 1.15612C18.3656 0.843563 17.9417 0.667969 17.4997 0.667969ZM17.4997 6.5013H11.6663V8.16797H17.4997V12.3346H2.49967V8.16797H6.66634V10.668L9.99967 7.33464L6.66634 4.0013V6.5013H2.49967V2.33464H17.4997V6.5013Z"
        fill="#3026B9"
      />
    </SvgIcon>
  );
};
export const PopoutIcon = (props) => {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18 21.9993H3.33333C2.97971 21.9993 2.64057 21.8589 2.39052 21.6088C2.14048 21.3588 2 21.0196 2 20.666V5.99935C2 5.64573 2.14048 5.30659 2.39052 5.05654C2.64057 4.80649 2.97971 4.66602 3.33333 4.66602H9.33333C9.70152 4.66602 10 4.96449 10 5.33268C10 5.70087 9.70152 5.99935 9.33333 5.99935H4.33333C3.78105 5.99935 3.33333 6.44706 3.33333 6.99935V19.666C3.33333 20.2183 3.78105 20.666 4.33333 20.666H17C17.5523 20.666 18 20.2183 18 19.666V14.666C18 14.2978 18.2985 13.9993 18.6667 13.9993C19.0349 13.9993 19.3333 14.2978 19.3333 14.666V20.666C19.3333 21.0196 19.1929 21.3588 18.9428 21.6088C18.6928 21.8589 18.3536 21.9993 18 21.9993Z"
        fill="#0D0AB5"
      />
      <path
        d="M12.0014 2C11.8246 2 11.655 2.07024 11.53 2.19526C11.405 2.32029 11.3347 2.48986 11.3347 2.66667C11.3347 2.84348 11.405 3.01305 11.53 3.13807C11.655 3.2631 11.8246 3.33333 12.0014 3.33333H18.4244C18.9055 3.33333 19.1464 3.91499 18.8062 4.25517L10.4947 12.5667C10.425 12.6264 10.3683 12.7 10.3283 12.7827C10.2882 12.8654 10.2658 12.9555 10.2622 13.0473C10.2587 13.1391 10.2741 13.2307 10.3077 13.3162C10.3412 13.4018 10.392 13.4795 10.457 13.5444C10.522 13.6094 10.5996 13.6602 10.6852 13.6938C10.7707 13.7273 10.8623 13.7427 10.9541 13.7392C11.0459 13.7357 11.136 13.7132 11.2187 13.6732C11.3014 13.6331 11.375 13.5765 11.4347 13.5067L19.7462 5.19517C20.0864 4.85499 20.6681 5.09592 20.6681 5.57701V12C20.6681 12.1768 20.7383 12.3464 20.8633 12.4714C20.9884 12.5964 21.1579 12.6667 21.3347 12.6667C21.5116 12.6667 21.6811 12.5964 21.8062 12.4714C21.9312 12.3464 22.0014 12.1768 22.0014 12V2.54C22.0014 2.24177 21.7596 2 21.4614 2H12.0014Z"
        fill="#0D0AB5"
      />
    </SvgIcon>
  );
};
export const KeyBoardArrowDown = (props) => {
  const classes = useStyles();
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.59 8.58984L12 13.1698L7.41 8.58984L6 9.99984L12 15.9998L18 9.99984L16.59 8.58984Z" fill="#424242" />
    </svg>
  );
};
export const KeyBoardArrowUp = (props) => {
  const classes = useStyles();
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7.41 15.4102L12 10.8302L16.59 15.4102L18 14.0002L12 8.00016L6 14.0002L7.41 15.4102Z" fill="#424242" />
    </svg>
  );
};

export function TxtIcon() {
  return (
    <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.9094 8.58481H19.3331V5.80378C19.3331 5.78645 19.3304 5.76898 19.3281 5.75143C19.3272 5.64099 19.292 5.53257 19.2164 5.44664L14.5892 0.160951C14.5877 0.159594 14.5864 0.159113 14.5854 0.157668C14.5579 0.126852 14.5257 0.101202 14.4916 0.0790529C14.4816 0.072312 14.4715 0.0666653 14.4609 0.0607561C14.4314 0.0445938 14.4003 0.0315101 14.3681 0.0217111C14.3593 0.0193036 14.3515 0.0158456 14.3428 0.0134819C14.3075 0.00480092 14.2712 0.000276331 14.2348 0L2.86254 0C2.34327 0 1.92139 0.42236 1.92139 0.941194V8.58455H1.34508C0.602352 8.58455 0 9.18655 0 9.92968V16.925C0 17.6674 0.602352 18.2702 1.34508 18.2702H1.92139V23.0588C1.92139 23.5777 2.34327 24 2.86254 24H18.3919C18.9108 24 19.3331 23.5777 19.3331 23.0588V18.2702H19.9094C20.652 18.2702 21.2545 17.6678 21.2545 16.9251V9.92994C21.2545 9.1866 20.6521 8.58481 19.9094 8.58481ZM2.86254 0.941194H13.7642V5.75642C13.7642 6.01656 13.9751 6.22701 14.2348 6.22701H18.392V8.58477H2.86254V0.941194ZM7.50491 9.85062H9.32934L9.94491 11.1332C10.1531 11.5602 10.3098 11.9045 10.4766 12.3008H10.4976C10.6639 11.8525 10.7997 11.5395 10.9769 11.1332L11.5717 9.85062H13.386L11.4048 13.2808L13.49 16.8782H11.6548L11.019 15.6062C10.7583 15.116 10.5913 14.7514 10.3931 14.3444H10.3722C10.2262 14.7514 10.0491 15.116 9.83022 15.6062L9.24648 16.8782H7.43189L9.46534 13.3228L7.50491 9.85062ZM1.51973 11.1852V9.85067H6.92046V11.1852H5.002V16.8778H3.4072V11.1852H1.51973ZM18.392 22.8037H2.86254V18.2702H18.3919V22.8037H18.392ZM19.2775 11.1852H17.3589V16.8778H15.7637V11.1852H13.8763V9.85067H19.2775V11.1852H19.2775Z"
        fill="#212121"
      />
    </svg>
  );
}

export function PdfIcon() {
  return (
    <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.9094 8.58469H19.3331V5.80404C19.3331 5.7867 19.3304 5.76923 19.3281 5.75167C19.3272 5.64125 19.292 5.5328 19.2164 5.44687L14.5891 0.160958C14.5877 0.159608 14.5864 0.159128 14.5854 0.157647C14.5578 0.126841 14.5257 0.10122 14.4917 0.0790413C14.4817 0.0725149 14.4714 0.0664235 14.4609 0.0607842C14.4314 0.044583 14.4003 0.0314987 14.3681 0.0217429C14.3594 0.0193028 14.3515 0.0157298 14.3428 0.013464C14.3074 0.00480273 14.2712 0.000284351 14.2348 0L2.86257 0C2.34326 0 1.92139 0.422352 1.92139 0.941218V8.58447H1.34523C0.602352 8.58447 0 9.18656 0 9.9297V16.9251C0 17.6677 0.602352 18.2702 1.34523 18.2702H1.92143V23.0588C1.92143 23.5776 2.34331 24 2.86261 24H18.392C18.9108 24 19.3332 23.5777 19.3332 23.0588V18.2702H19.9095C20.6521 18.2702 21.2546 17.6677 21.2546 16.9251V9.92992C21.2546 9.18682 20.6521 8.58469 19.9094 8.58469ZM2.86257 0.941393H13.7642V5.75676C13.7642 6.01676 13.9751 6.22735 14.2348 6.22735H18.3919V8.585H2.86257V0.941393ZM14.4163 13.465C14.4163 14.8104 13.9264 15.738 13.2477 16.3118C12.5082 16.9269 11.3819 17.2187 10.006 17.2187C9.18251 17.2187 8.59876 17.1665 8.20208 17.1146V10.212C8.78591 10.1183 9.54752 10.0662 10.3504 10.0662C11.6842 10.0662 12.5499 10.3062 13.2279 10.8168C13.9574 11.359 14.4163 12.2242 14.4163 13.465ZM2.14361 17.1459V10.212C2.63342 10.129 3.32191 10.0662 4.29158 10.0662C5.27158 10.0662 5.97032 10.2539 6.43933 10.6295C6.88779 10.9838 7.18944 11.5678 7.18944 12.256C7.18944 12.9444 6.96068 13.5282 6.5433 13.9242C6.00116 14.4352 5.19842 14.6647 4.25977 14.6647C4.05158 14.6647 3.86326 14.6541 3.71795 14.6332V17.1461H2.14361V17.1459ZM18.392 22.804H2.86257V18.2702H18.3919V22.804H18.392ZM19.7557 11.4216H17.0547V13.0273H19.5781V14.3203H17.0547V17.1459H15.4602V10.1183H19.7557V11.4216Z"
        fill="#D40000"
      />
    </svg>
  );
}

export function ImageIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21.3333 21.3333H2.66667V2.66667H21.3333M21.3333 0H2.66667C1.95942 0 1.28115 0.280951 0.781048 0.781048C0.280951 1.28115 0 1.95942 0 2.66667V21.3333C0 22.0406 0.280951 22.7189 0.781048 23.219C1.28115 23.719 1.95942 24 2.66667 24H21.3333C22.0406 24 22.7189 23.719 23.219 23.219C23.719 22.7189 24 22.0406 24 21.3333V2.66667C24 1.95942 23.719 1.28115 23.219 0.781048C22.7189 0.280951 22.0406 0 21.3333 0ZM14.6133 12.3867L10.9467 17.1067L8.33333 13.96L4.66667 18.6667H19.3333L14.6133 12.3867Z" fill="#1D1D11" />
    </SvgIcon>
  );
}

export function DocumentIcon() {
  return (
    <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.9094 8.58481H19.3332V5.80378C19.3332 5.78645 19.3304 5.76898 19.3281 5.75143C19.3272 5.64099 19.2921 5.53257 19.2165 5.44664L14.5892 0.160951C14.5878 0.159594 14.5864 0.159113 14.5854 0.157668C14.5579 0.126852 14.5257 0.101202 14.4917 0.0790529C14.4817 0.0725433 14.4714 0.0664394 14.4609 0.0607561C14.4314 0.0445863 14.4003 0.0315022 14.3681 0.0217111C14.3594 0.0193036 14.3516 0.0158456 14.3429 0.0134819C14.3078 0.00516514 14.2715 0 14.2349 0H2.86258C2.34327 0 1.92143 0.42236 1.92143 0.941194V8.58455H1.34526C0.602352 8.58455 0 9.18655 0 9.92968V16.925C0 17.6674 0.602352 18.2702 1.34526 18.2702H1.92143V23.0588C1.92143 23.5777 2.34327 24 2.86258 24H18.392C18.9109 24 19.3332 23.5777 19.3332 23.0588V18.2702H19.9094C20.6521 18.2702 21.2545 17.6678 21.2545 16.9251V9.92994C21.2546 9.1866 20.6521 8.58481 19.9094 8.58481ZM2.86258 0.941194H13.7642V5.75642C13.7642 6.01656 13.9751 6.22701 14.2348 6.22701H18.392V8.58477H2.86258V0.941194ZM14.1551 13.045C14.1551 15.1845 12.8579 16.4814 10.9506 16.4814C9.01457 16.4814 7.88187 15.0197 7.88187 13.1613C7.88187 11.2057 9.13061 9.74404 11.057 9.74404C13.0606 9.74404 14.1551 11.2442 14.1551 13.045ZM1.44383 16.346V9.93795C1.98591 9.85062 2.69244 9.80217 3.43819 9.80217C4.67717 9.80217 5.48048 10.0249 6.10975 10.4991C6.78735 11.0026 7.21351 11.8062 7.21351 12.9579C7.21351 14.2068 6.75863 15.0682 6.12953 15.6006C5.44213 16.1718 4.39654 16.4427 3.11861 16.4427C2.35342 16.4427 1.81156 16.3943 1.44383 16.346ZM18.392 22.8037H2.86258V18.2702H18.392V22.8037ZM18.4632 15.2524C18.9085 15.2524 19.4023 15.155 19.6925 15.0393L19.9155 16.1912C19.6443 16.3272 19.0345 16.4717 18.2408 16.4717C15.9853 16.4717 14.8236 15.0682 14.8236 13.2095C14.8236 10.9836 16.4111 9.74404 18.3855 9.74404C19.1502 9.74404 19.7316 9.8989 19.9931 10.0346L19.6928 11.2057C19.3929 11.08 18.9766 10.9636 18.4536 10.9636C17.2824 10.9636 16.3723 11.6704 16.3723 13.1223C16.3721 14.4292 17.1466 15.2524 18.4632 15.2524Z"
        fill="#2065C2"
      />
    </svg>
  );
}

export function PPTIcon() {
  return (
    <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.9094 8.58469H19.3331V5.80404C19.3331 5.7867 19.3304 5.76923 19.3281 5.75167C19.3272 5.64125 19.292 5.5328 19.2164 5.44687L14.5891 0.160958L14.5854 0.157647C14.5578 0.126841 14.5257 0.10122 14.4917 0.0790413C14.4817 0.0725149 14.4714 0.0664235 14.4609 0.0607842C14.4314 0.044583 14.4003 0.0314987 14.3681 0.0217429C14.3594 0.0193028 14.3515 0.0157298 14.3428 0.013464C14.3074 0.00480273 14.2712 0.000284351 14.2348 0L2.86257 0C2.34326 0 1.92139 0.422352 1.92139 0.941218V8.58447H1.34523C0.602352 8.58447 0 9.18656 0 9.9297V16.9251C0 17.6677 0.602352 18.2702 1.34523 18.2702H1.92143V23.0588C1.92143 23.5776 2.34331 24 2.86261 24H18.392C18.9108 24 19.3332 23.5777 19.3332 23.0588V18.2702H19.9095C20.6521 18.2702 21.2546 17.6677 21.2546 16.9251V9.92992C21.2546 9.18682 20.6521 8.58469 19.9094 8.58469ZM2.86257 0.941393H13.7642V5.75676C13.7642 6.01676 13.9751 6.22735 14.2348 6.22735H18.3919V8.585H2.86257V0.941393ZM13.6788 11.9845C13.6788 12.6727 13.4494 13.2564 13.0326 13.6526C12.4899 14.1632 11.687 14.3929 10.7485 14.3929C10.5402 14.3929 10.3525 14.3821 10.2066 14.3616V16.8745H8.63201V9.94072C9.12212 9.85746 9.81066 9.79458 10.7802 9.79458C11.7601 9.79458 12.4588 9.98242 12.9279 10.3579C13.3764 10.7126 13.6788 11.2963 13.6788 11.9845ZM2.57337 16.8745V9.94072C3.06352 9.85746 3.75202 9.79458 4.7216 9.79458C5.7016 9.79458 6.40025 9.98242 6.86922 10.3579C7.31785 10.7126 7.62003 11.2963 7.62003 11.9845C7.62003 12.6728 7.39083 13.2564 6.97389 13.6526C6.43123 14.1634 5.6284 14.393 4.68988 14.393C4.48156 14.393 4.29385 14.3822 4.1477 14.3617V16.8746L2.57337 16.8745ZM18.392 22.804H2.86257V18.2702H18.3919L18.392 22.804ZM19.5601 11.1816H17.6415V16.8745H16.0464V11.1816H14.159V9.84708H19.5601V11.1816Z"
        fill="#CA411D"
      />
    </svg>
  );
}

export function ExcelIcon() {
  return (
    <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M20.2825 8.58469H19.7062V5.80404C19.7062 5.7867 19.7034 5.76923 19.7013 5.75154C19.7004 5.64125 19.665 5.53276 19.5895 5.44687L14.9619 0.160958C14.9605 0.159608 14.9592 0.159128 14.9582 0.157647C14.9306 0.126841 14.8985 0.10122 14.8645 0.0790412C14.8544 0.0722874 14.8443 0.066623 14.8337 0.0606535C14.8042 0.0445066 14.7731 0.0314246 14.7409 0.0216122C14.7322 0.0193028 14.7243 0.0157298 14.7156 0.0134205C14.6802 0.00479471 14.644 0.000291265 14.6076 0L3.23537 0C2.71607 0 2.29419 0.422352 2.29419 0.941218V8.58447H1.71803C0.975154 8.58447 0.372803 9.18656 0.372803 9.9297V16.9251C0.372803 17.6675 0.975154 18.2702 1.71803 18.2702H2.29424V23.0588C2.29424 23.5776 2.71611 24 3.23541 24H18.7648C19.2836 24 19.706 23.5776 19.706 23.0588V18.2702H20.2823C21.0249 18.2702 21.6274 17.6677 21.6274 16.9251V9.92992C21.6274 9.18656 21.0253 8.58469 20.2825 8.58469ZM3.23537 0.941218H14.137V5.75646C14.137 6.01646 14.3479 6.22705 14.6076 6.22705H18.7647V8.58469H3.23537V0.941218ZM13.7958 15.4398V16.8047H9.30546V9.61671H10.9377V15.4398H13.7958ZM2.23537 16.8045L4.3151 13.1679L2.30992 9.61671H4.17628L4.80543 10.9282C5.01889 11.3653 5.17894 11.7176 5.34966 12.1227H5.3707C5.54177 11.6639 5.68011 11.344 5.86129 10.9282L6.4693 9.61671H8.32499L6.29845 13.1254L8.43183 16.8046H6.55466L5.90416 15.5036C5.63772 15.0022 5.46687 14.6291 5.26434 14.2132H5.24299C5.09349 14.6293 4.91257 15.0027 4.68865 15.5036L4.09123 16.8046H2.23537V16.8045ZM18.7648 22.804H3.23537V18.2702H18.7647V22.804H18.7648ZM16.5148 16.9117C15.694 16.9117 14.8831 16.6981 14.4783 16.4743L14.8085 15.1305C15.2459 15.3543 15.9178 15.5786 16.6113 15.5786C17.3581 15.5786 17.7524 15.2693 17.7524 14.8001C17.7524 14.352 17.4112 14.0961 16.5472 13.7867C15.3528 13.3706 14.5741 12.7095 14.5741 11.6639C14.5741 10.4374 15.598 9.49907 17.2942 9.49907C18.1044 9.49907 18.7019 9.67018 19.1284 9.86168L18.7657 11.1736C18.4779 11.0348 17.9657 10.8323 17.2621 10.8323C16.5583 10.8323 16.2171 11.152 16.2171 11.5256C16.2171 11.9842 16.6219 12.1871 17.55 12.5391C18.8191 13.0083 19.4165 13.6692 19.4165 14.6825C19.4159 15.8879 18.4884 16.9117 16.5148 16.9117Z"
        fill="#15854B"
      />
    </svg>
  );
}

export function DefaultAttachmentIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="22" height="27" viewBox="0 0 22 27" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.6666 0.151855H2.99992C1.53325 0.151855 0.346586 1.35186 0.346586 2.81852L0.333252 24.1519C0.333252 25.6185 1.51992 26.8185 2.98659 26.8185H18.9999C20.4666 26.8185 21.6666 25.6185 21.6666 24.1519V8.15185L13.6666 0.151855ZM2.99992 24.1519V2.81852H12.3333V9.48519H18.9999V24.1519H2.99992Z" fill="black" />
    </SvgIcon>
  );
}
export function NotificationSnoozeicon(props) {
  const classes = useStyles();
  return (
    <SvgIcon
      {...props}
      // classes={{ root: classes.svgSize }}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M9.8219 19.044C10.1719 19.036 10.5077 18.9044 10.77 18.6726C11.0323 18.4408 11.2042 18.1237 11.2552 17.7773H8.33301C8.38549 18.1331 8.56541 18.4576 8.83929 18.6906C9.11316 18.9236 9.46235 19.0492 9.8219 19.044Z" fill="#1D1D1D" />
      <path
        d="M18.0337 16.1391C17.4979 15.6615 17.0289 15.114 16.6392 14.5113C16.2138 13.6794 15.9588 12.7709 15.8892 11.8391V9.09462C15.8929 7.63107 15.362 6.21655 14.3963 5.11683C13.4306 4.0171 12.0965 3.30787 10.6448 3.1224V2.40573C10.6448 2.20903 10.5666 2.02038 10.4275 1.88129C10.2884 1.7422 10.0998 1.66406 9.9031 1.66406C9.7064 1.66406 9.51775 1.7422 9.37866 1.88129C9.23958 2.02038 9.16144 2.20903 9.16144 2.40573V3.13351C7.72269 3.33236 6.40475 4.04587 5.45173 5.1419C4.49871 6.23793 3.97518 7.6422 3.9781 9.09462V11.8391C3.90852 12.7709 3.65354 13.6794 3.2281 14.5113C2.84526 15.1126 2.38377 15.66 1.85588 16.1391C1.79662 16.1911 1.74913 16.2552 1.71656 16.3271C1.68399 16.3989 1.66709 16.4768 1.66699 16.5557V17.3113C1.66699 17.4586 1.72552 17.5999 1.82971 17.7041C1.9339 17.8083 2.07521 17.8668 2.22255 17.8668H17.667C17.8143 17.8668 17.9556 17.8083 18.0598 17.7041C18.164 17.5999 18.2225 17.4586 18.2225 17.3113V16.5557C18.2224 16.4768 18.2055 16.3989 18.173 16.3271C18.1404 16.2552 18.0929 16.1911 18.0337 16.1391ZM2.82255 16.7557C3.33944 16.2564 3.79455 15.6968 4.1781 15.0891C4.714 14.0843 5.02668 12.9757 5.09477 11.8391V9.09462C5.07274 8.44353 5.18195 7.79466 5.41591 7.18666C5.64986 6.57865 6.00378 6.02394 6.45657 5.55556C6.90937 5.08718 7.45179 4.71471 8.05154 4.46033C8.65128 4.20594 9.29608 4.07484 9.94755 4.07484C10.599 4.07484 11.2438 4.20594 11.8436 4.46033C12.4433 4.71471 12.9857 5.08718 13.4385 5.55556C13.8913 6.02394 14.2452 6.57865 14.4792 7.18666C14.7131 7.79466 14.8224 8.44353 14.8003 9.09462V11.8391C14.8684 12.9757 15.1811 14.0843 15.717 15.0891C16.1005 15.6968 16.5557 16.2564 17.0725 16.7557H2.82255Z"
        fill="#1D1D1D"
      />
      <path d="M12.8824 11.1053C12.8824 12.704 11.5655 14 9.94118 14C8.31681 14 7 12.704 7 11.1053C7 9.50654 8.31681 8.21053 9.94118 8.21053C11.5655 8.21053 12.8824 9.50654 12.8824 11.1053Z" fill="white" />
      <path d="M8.83552 12.007V11.6301L10.1865 9.80736H8.83688V9.27015H11.0365V9.647L9.68549 11.4698H11.0351V12.007H8.83552Z" fill="black" />
      <path d="M17 6.18421C17 7.9428 15.5515 9.36842 13.7647 9.36842C11.9779 9.36842 10.5294 7.9428 10.5294 6.18421C10.5294 4.42562 11.9779 3 13.7647 3C15.5515 3 17 4.42562 17 6.18421Z" fill="white" />
      <path d="M12.0149 8.21012V7.63035L14.0934 4.82607H12.017V3.99959H15.401V4.57936L13.3226 7.38364H15.399V8.21012H12.0149Z" fill="black" />
    </SvgIcon>
  );
}

export function FullScreenIcon(props) {
  const classes = useStyles();
  return (
    <SvgIcon {...props} classes={{ root: classes.svgSize }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M1.75 1.5C1.6837 1.5 1.62011 1.52634 1.57322 1.57322C1.52634 1.62011 1.5 1.6837 1.5 1.75V5.25C1.5 5.44891 1.42098 5.63968 1.28033 5.78033C1.13968 5.92098 0.948912 6 0.75 6C0.551088 6 0.360322 5.92098 0.21967 5.78033C0.0790175 5.63968 0 5.44891 0 5.25V1.75C0 0.784 0.784 0 1.75 0H5.25C5.44891 0 5.63968 0.0790175 5.78033 0.21967C5.92098 0.360322 6 0.551088 6 0.75C6 0.948912 5.92098 1.13968 5.78033 1.28033C5.63968 1.42098 5.44891 1.5 5.25 1.5H1.75ZM12 0.75C12 0.551088 12.079 0.360322 12.2197 0.21967C12.3603 0.0790175 12.5511 0 12.75 0H16.25C17.216 0 18 0.784 18 1.75V5.25C18 5.44891 17.921 5.63968 17.7803 5.78033C17.6397 5.92098 17.4489 6 17.25 6C17.0511 6 16.8603 5.92098 16.7197 5.78033C16.579 5.63968 16.5 5.44891 16.5 5.25V1.75C16.5 1.6837 16.4737 1.62011 16.4268 1.57322C16.3799 1.52634 16.3163 1.5 16.25 1.5H12.75C12.5511 1.5 12.3603 1.42098 12.2197 1.28033C12.079 1.13968 12 0.948912 12 0.75ZM0.75 12C0.948912 12 1.13968 12.079 1.28033 12.2197C1.42098 12.3603 1.5 12.5511 1.5 12.75V16.25C1.5 16.388 1.612 16.5 1.75 16.5H5.25C5.44891 16.5 5.63968 16.579 5.78033 16.7197C5.92098 16.8603 6 17.0511 6 17.25C6 17.4489 5.92098 17.6397 5.78033 17.7803C5.63968 17.921 5.44891 18 5.25 18H1.75C1.28587 18 0.840752 17.8156 0.512563 17.4874C0.184375 17.1592 0 16.7141 0 16.25V12.75C0 12.5511 0.0790175 12.3603 0.21967 12.2197C0.360322 12.079 0.551088 12 0.75 12ZM17.25 12C17.4489 12 17.6397 12.079 17.7803 12.2197C17.921 12.3603 18 12.5511 18 12.75V16.25C18 16.7141 17.8156 17.1592 17.4874 17.4874C17.1592 17.8156 16.7141 18 16.25 18H12.75C12.5511 18 12.3603 17.921 12.2197 17.7803C12.079 17.6397 12 17.4489 12 17.25C12 17.0511 12.079 16.8603 12.2197 16.7197C12.3603 16.579 12.5511 16.5 12.75 16.5H16.25C16.3163 16.5 16.3799 16.4737 16.4268 16.4268C16.4737 16.3799 16.5 16.3163 16.5 16.25V12.75C16.5 12.5511 16.579 12.3603 16.7197 12.2197C16.8603 12.079 17.0511 12 17.25 12Z"
        fill="#323232"
      />
    </SvgIcon>
  );
}

export function FullScreentExitIcon() {
  return (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0.75 9H3V11.25H4.5V7.5H0.75V9ZM3 3H0.75V4.5H4.5V0.75H3V3ZM7.5 11.25H9V9H11.25V7.5H7.5V11.25ZM9 3V0.75H7.5V4.5H11.25V3H9Z" fill="#323232" />
    </svg>
  );
}

export function InfoIcon() {
  return (
    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.1602 0.0336787C9.83491 0.192808 11.397 0.907683 12.572 2.05268C13.8521 3.28828 14.6089 4.92824 14.6982 6.65998C14.7875 8.39171 14.203 10.0941 13.0561 11.4427C12.0013 12.6885 10.5211 13.5444 8.8719 13.8622C7.22271 14.18 5.50849 13.9396 4.02624 13.1827C2.5408 12.4094 1.38118 11.1733 0.742641 9.68268C0.101241 8.18438 0.0158698 6.52323 0.500581 4.97268C0.98425 3.42817 2.01393 2.08952 3.41582 1.18268C4.80593 0.28159 6.48575 -0.125233 8.1602 0.0336787ZM8.65695 12.8827C10.0702 12.6103 11.3393 11.8783 12.2457 10.8127C13.2273 9.65362 13.7266 8.19243 13.649 6.70658C13.5714 5.22073 12.9222 3.8137 11.8248 2.75268C10.8196 1.77769 9.4857 1.1694 8.05633 1.03413C6.62697 0.898866 5.19304 1.24523 4.00519 2.01268C3.11108 2.59807 2.38907 3.39068 1.90755 4.31541C1.42604 5.24013 1.20097 6.26636 1.25367 7.29686C1.30637 8.32737 1.63509 9.32803 2.20869 10.204C2.78229 11.0801 3.58177 11.8024 4.5314 12.3027C5.79256 12.9494 7.25273 13.1547 8.65695 12.8827ZM6.90464 5.00268H8.22019V4.00268H6.90464V5.00268ZM8.22019 6.00268V10.0027H6.90464V6.00268H8.22019Z"
        fill="#616166"
      />
    </svg>
  );
}

export function SubstitutionIcon() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_20362_140249)">
        <g clip-path="url(#clip1_20362_140249)">
          <path d="M22.519 23.2529H11.6314C11.2504 23.2529 10.9338 22.958 10.9074 22.5781C10.902 22.5038 10.7948 20.7366 12.1295 19.3047C13.2074 18.1487 14.8713 17.5625 17.0753 17.5625C19.2791 17.5625 20.9429 18.1487 22.021 19.3047C23.3562 20.7366 23.2486 22.5036 23.2433 22.5781C23.2167 22.958 22.9002 23.2529 22.5191 23.2529H22.519ZM12.4245 21.801H21.7254C21.6333 21.3644 21.4227 20.7845 20.9447 20.2791C20.1508 19.4392 18.849 19.0139 17.0755 19.0139C15.2907 19.0139 13.9835 19.445 13.1916 20.2948C12.7226 20.7973 12.5157 21.3694 12.4246 21.801H12.4245Z" fill="#424242" />
          <path d="M17.0777 17.4238C15.2825 17.4238 13.8223 15.9636 13.8223 14.1689C13.8225 12.3741 15.2825 10.9141 17.0777 10.9141C18.8724 10.9141 20.3326 12.3743 20.3326 14.1691C20.3326 15.9636 18.8724 17.4238 17.0777 17.4238ZM17.0777 12.3658C16.0832 12.3658 15.2741 13.1747 15.2741 14.169C15.2741 15.1635 16.0832 15.9722 17.0777 15.9722C18.0721 15.9722 18.8809 15.1634 18.8809 14.169C18.881 13.1747 18.0722 12.3658 17.0777 12.3658Z" fill="#424242" />
          <path d="M6.19372 7.39844C0.452472 7.39844 0.749687 11.6374 0.749687 11.6374H11.6373C11.6373 11.6374 11.9348 7.39844 6.19323 7.39844H6.19372Z" fill="#424242" />
          <path d="M8.7224 3.27907C8.7224 4.67588 7.58995 5.80834 6.19313 5.80834C4.79632 5.80834 3.66406 4.67588 3.66406 3.27907C3.66406 1.88226 4.79632 0.75 6.19313 0.75C7.58995 0.75 8.7224 1.88226 8.7224 3.27907Z" fill="#424242" />
          <path d="M9.67243 20.392H7.28647C6.28408 20.392 5.46875 19.5768 5.46875 18.5746V14.1634C5.46875 13.7622 5.79368 13.4375 6.19467 13.4375C6.59546 13.4375 6.92059 13.7622 6.92059 14.1634V18.5745C6.92059 18.7764 7.08478 18.9402 7.2867 18.9402H9.67266C10.0735 18.9402 10.3986 19.2649 10.3986 19.6661C10.3982 20.0673 10.0733 20.392 9.67246 20.392L9.67243 20.392Z" fill="#424242" />
          <path d="M17.3025 9.82953C16.9018 9.82953 16.5766 9.5048 16.5766 9.10361V4.69257C16.5766 4.49064 16.4124 4.32684 16.2105 4.32684H13.8246C13.4238 4.32684 13.0986 4.00211 13.0986 3.60092C13.0986 3.19974 13.4236 2.875 13.8246 2.875H16.2105C17.2129 2.875 18.0282 3.69018 18.0282 4.69237V9.10341C18.0284 9.50478 17.7033 9.82953 17.3025 9.82953H17.3025Z" fill="#424242" />
          <path d="M17.302 9.83003C17.1163 9.83003 16.9305 9.75914 16.7887 9.61737L14.8865 7.71554C14.6029 7.432 14.6029 6.97275 14.8865 6.68922C15.17 6.40569 15.6293 6.40568 15.9128 6.68922L17.3018 8.07779L18.6903 6.68922C18.9739 6.40568 19.4331 6.40568 19.7167 6.68922C20.0002 6.97276 20.0002 7.43201 19.7167 7.71554L17.8148 9.61737C17.6734 9.75914 17.4876 9.83003 17.302 9.83003Z" fill="#424242" />
          <path d="M4.29037 16.791C4.10472 16.791 3.91889 16.7201 3.77711 16.5783C3.49357 16.2948 3.49357 15.8355 3.77711 15.552L5.67894 13.6502C5.96248 13.3666 6.42173 13.3666 6.70526 13.6502L8.60749 15.552C8.89103 15.8355 8.89103 16.2948 8.60749 16.5783C8.32395 16.8618 7.8647 16.8619 7.58116 16.5783L6.192 15.1897L4.80343 16.5783C4.66166 16.7201 4.47601 16.791 4.29037 16.791Z" fill="#424242" />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_20362_140249">
          <rect width="24" height="24" fill="white" />
        </clipPath>
        <clipPath id="clip1_20362_140249">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function BasicFilterHoverIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path d="M0.24969 1.61C2.26969 4.2 5.99969 9 5.99969 9V15C5.99969 15.55 6.44969 16 6.99969 16H8.99969C9.54969 16 9.99969 15.55 9.99969 15V9C9.99969 9 13.7197 4.2 15.7397 1.61C16.2497 0.95 15.7797 0 14.9497 0H1.03969C0.20969 0 -0.26031 0.95 0.24969 1.61Z" fill=" #3026b9" />
    </svg>
  );
}

export function BasicFilterIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path d="M3.00018 2H13.0002L7.99018 8.3L3.00018 2ZM0.250178 1.61C2.27018 4.2 6.00018 9 6.00018 9V15C6.00018 15.55 6.45018 16 7.00018 16H9.00018C9.55018 16 10.0002 15.55 10.0002 15V9C10.0002 9 13.7202 4.2 15.7402 1.61C16.2502 0.95 15.7802 0 14.9502 0H1.04018C0.210178 0 -0.259822 0.95 0.250178 1.61Z" fill="#323232" />
    </svg>
  );
}

export function BasicFilterExpandIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="6" height="10" viewBox="0 0 6 10" fill="none">
      <path d="M0.4425 1.5575L3.8775 5L0.4425 8.4425L1.5 9.5L6 5L1.5 0.500001L0.4425 1.5575Z" fill="#323232" />
    </svg>
  );
}

export function BasicFilterCondenseIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6" fill="none">
      <path d="M8.4425 0.4425L5 3.8775L1.5575 0.4425L0.5 1.5L5 6L9.5 1.5L8.4425 0.4425Z" fill="#323232" />
    </svg>
  );
}

export function BasicFilterChipDeleteIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
      <path d="M11.8346 1.34297L10.6596 0.167969L6.0013 4.8263L1.34297 0.167969L0.167969 1.34297L4.8263 6.0013L0.167969 10.6596L1.34297 11.8346L6.0013 7.1763L10.6596 11.8346L11.8346 10.6596L7.1763 6.0013L11.8346 1.34297Z" fill="#8F8F8F" />
    </svg>
  );
}

export function CheckCircleRounded() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <circle cx="12.5" cy="12.875" r="12" fill="#0BB282" />
      <path d="M10.6818 15.2034L8.29545 12.6959L7.5 13.5317L10.6818 16.875L17.5 9.71082L16.7045 8.875L10.6818 15.2034Z" fill="white" />
    </svg>
  );
}

export function ApprovedCheckCircleRounded() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
      <circle cx="8.5" cy="8.875" r="7.5" fill="#B3E3D5" stroke="#0BB282" />
      <path d="M6.68182 11.2034L4.29545 8.6959L3.5 9.53172L6.68182 12.875L13.5 5.71082L12.7045 4.875L6.68182 11.2034Z" fill="#0BB282" />
    </svg>
  );
}

export function ProcessFlowEllipseIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
      <circle cx="8.5" cy="8.875" r="8" fill="#453CC0" />
    </svg>
  );
}

export function ProcessFlowEndIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
      <g clip-path="url(#clip0_19541_31516)">
        <path
          d="M12.7348 13.3502L16.3798 9.70523C16.4617 9.60958 16.5045 9.48654 16.4996 9.3607C16.4948 9.23486 16.4426 9.11549 16.3536 9.02645C16.2645 8.9374 16.1451 8.88523 16.0193 8.88037C15.8935 8.87551 15.7704 8.91832 15.6748 9.00023L12.0298 12.6452L8.38477 8.99523C8.28912 8.91332 8.16608 8.87051 8.04025 8.87537C7.91441 8.88023 7.79504 8.9324 7.70599 9.02145C7.61694 9.11049 7.56478 9.22986 7.55992 9.3557C7.55506 9.48154 7.59786 9.60458 7.67977 9.70023L11.3248 13.3502L7.67477 16.9952C7.62243 17.0401 7.57992 17.0952 7.54991 17.1572C7.5199 17.2193 7.50303 17.2868 7.50037 17.3557C7.49771 17.4246 7.50932 17.4932 7.53445 17.5574C7.55959 17.6216 7.59772 17.6798 7.64645 17.7286C7.69517 17.7773 7.75345 17.8154 7.81761 17.8405C7.88177 17.8657 7.95044 17.8773 8.0193 17.8746C8.08816 17.872 8.15572 17.8551 8.21776 17.8251C8.27979 17.7951 8.33495 17.7526 8.37977 17.7002L12.0298 14.0552L15.6748 17.7002C15.7704 17.7821 15.8935 17.8249 16.0193 17.8201C16.1451 17.8152 16.2645 17.7631 16.3536 17.674C16.4426 17.585 16.4948 17.4656 16.4996 17.3398C16.5045 17.2139 16.4617 17.0909 16.3798 16.9952L12.7348 13.3502Z"
          fill="#B71C1C"
        />
      </g>
      <rect x="1" y="1.375" width="23" height="23" rx="11.5" stroke="#B71C1C" />
      <defs>
        <clipPath id="clip0_19541_31516">
          <rect width="18" height="18" fill="white" transform="translate(3.5 3.875)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ConnectedAppsIcon() {
  return (
    <svg width="24" height="24" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.89025 16.8374H5.1892H5.18893C6.11034 18.5002 7.52881 19.8295 9.24382 20.6375C9.22298 20.819 9.21427 21.0019 9.21744 21.1846C9.21744 22.5478 9.9412 23.8072 11.1164 24.4887C12.2913 25.1704 13.7391 25.1704 14.914 24.4887C16.089 23.8072 16.813 22.5478 16.813 21.1846C16.8061 21.0011 16.7871 20.8185 16.7563 20.6375C18.4721 19.8308 19.8908 18.501 20.8111 16.8374H21.1101H21.1098C22.4613 16.8753 23.7302 16.1861 24.4386 15.0295C25.1473 13.8728 25.1877 12.4241 24.5447 11.2295C23.9016 10.0345 22.6731 9.27506 21.3217 9.23719C20.4758 7.09992 18.8533 5.36552 16.7826 4.38448C16.6457 3.1331 15.9005 2.03114 14.7932 1.44193C13.6858 0.852711 12.3597 0.85267 11.2522 1.44193C10.1448 2.03119 9.39997 3.1331 9.26278 4.38448C7.17492 5.35595 5.53503 7.09191 4.67833 9.23719C3.32686 9.2751 2.09839 10.0345 1.45535 11.2295C0.812339 12.4241 0.852705 13.8728 1.56142 15.0295C2.26984 16.1861 3.53871 16.8752 4.89018 16.8374H4.89025ZM12.9999 24.4375C12.1418 24.4386 11.3186 24.0969 10.7115 23.4881C10.1041 22.8792 9.76266 22.053 9.76184 21.1909C9.76132 20.3292 10.1014 19.5021 10.7078 18.8925C11.3141 18.2825 12.1368 17.9398 12.9948 17.9393C13.8529 17.939 14.6756 18.2812 15.2824 18.8906C15.8893 19.5 16.2302 20.3265 16.2302 21.1885C16.2291 22.0492 15.8887 22.8744 15.2832 23.4835C14.6776 24.0924 13.8565 24.4354 12.9998 24.4375H12.9999ZM24.3477 13.0373C24.3477 13.898 24.0073 14.7234 23.4015 15.332C22.7957 15.9405 21.9741 16.2826 21.1174 16.2826C20.2606 16.2826 19.439 15.9406 18.8332 15.332C18.2275 14.7234 17.887 13.8979 17.887 13.0373C17.887 12.1766 18.2274 11.3511 18.8332 10.7426C19.439 10.134 20.2607 9.79201 21.1174 9.79201C21.9739 9.79307 22.795 10.1353 23.4005 10.7436C24.006 11.3519 24.3467 12.1768 24.3477 13.0373ZM12.9999 1.63702C13.8567 1.63702 14.6783 1.97897 15.284 2.58759C15.8898 3.1962 16.2302 4.02164 16.2302 4.8823C16.2302 5.74301 15.8899 6.56845 15.284 7.177C14.6782 7.78556 13.8566 8.12757 12.9999 8.12757C12.1432 8.12757 11.3215 7.78562 10.7157 7.177C10.11 6.56839 9.76954 5.74295 9.76954 4.8823C9.7706 4.02185 10.1112 3.19696 10.7167 2.5886C11.3223 1.98026 12.1434 1.63804 12.9999 1.63702ZM9.21728 5.0191C9.21728 6.38156 9.94077 7.64041 11.1152 8.32164C12.2896 9.00288 13.7366 9.00288 14.911 8.32164C16.0854 7.64041 16.8089 6.38156 16.8089 5.0191C18.5601 5.92114 19.9385 7.41485 20.7013 9.23719C19.4925 9.34481 18.4087 10.0282 17.7868 11.0752C17.1651 12.122 17.081 13.4049 17.5604 14.5246C18.0401 15.6443 19.0253 16.465 20.2095 16.7311C19.3646 18.1673 18.1193 19.3232 16.6274 20.0562C16.3169 19.0006 15.5662 18.132 14.5694 17.6756C13.5725 17.2188 12.4274 17.2188 11.4305 17.6756C10.4337 18.132 9.68304 19.0006 9.37246 20.0562C7.88063 19.3232 6.63528 18.1673 5.7904 16.7311C6.97456 16.465 7.9598 15.6443 8.43951 14.5246C8.91894 13.4049 8.83477 12.122 8.21312 11.0752C7.59122 10.0282 6.5073 9.34482 5.29862 9.23719C6.07122 7.41431 7.45876 5.92325 9.2174 5.02677L9.21728 5.0191ZM4.89012 9.79201C5.74634 9.79413 6.56689 10.1377 7.17116 10.747C7.77539 11.3565 8.11418 12.1821 8.11283 13.0423C8.11151 13.9025 7.77008 14.7271 7.164 15.3346C6.55766 15.9422 5.73627 16.2831 4.87985 16.2826C4.02363 16.2818 3.20253 15.9395 2.59752 15.3312C1.99224 14.7226 1.65214 13.8974 1.65214 13.0372C1.65319 12.1755 1.99488 11.3495 2.60198 10.7409C3.20937 10.1323 4.03234 9.79085 4.89012 9.79194V9.79201Z"
        fill="#1D1D11"
        stroke="black"
        stroke-width="0.4"
      />
    </svg>
  );
}
export function ExitIcon() {
  return (
    <svg width="39" height="30" viewBox="0 0 42 34" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M27 9.5V5.75C27 4.75544 26.6049 3.80161 25.9017 3.09835C25.1984 2.39509 24.2446 2 23.25 2H5.25C4.25544 2 3.30161 2.39509 2.59835 3.09835C1.89509 3.80161 1.5 4.75544 1.5 5.75V28.25C1.5 29.2446 1.89509 30.1984 2.59835 30.9017C3.30161 31.6049 4.25544 32 5.25 32H23.25C24.2446 32 25.1984 31.6049 25.9017 30.9017C26.6049 30.1984 27 29.2446 27 28.25V24.5M33 9.5L40.5 17L33 24.5M14.9062 17H40.5" stroke="black" stroke-width="2.5625" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  );
}

export function CompletedStatusIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      {" "}
      <circle cx="7" cy="7" r="6.63158" fill="white" stroke="#4CAF50" stroke-width="0.736842" /> <circle cx="7" cy="7" r="6" fill="#4CAF50" />{" "}
    </svg>
  );
}

export function RejectedStatusIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      {" "}
      <circle cx="7" cy="7" r="6.63158" fill="white" stroke="#FF0101" stroke-width="0.736842" /> <circle cx="7" cy="7" r="6" fill="#FF0101" />{" "}
    </svg>
  );
}

export function InprogressStatusIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      {" "}
      <circle cx="7" cy="7" r="6.63158" fill="white" stroke="#CD722C" stroke-width="0.736842" /> <path d="M1.00732 7.29959L2.25951 7.65736C2.46983 7.71745 2.69088 7.72996 2.90664 7.69401L6.73357 7.05618C6.90972 7.02683 7.08974 7.02973 7.26485 7.06475L10.3792 7.68761C10.5803 7.72784 10.7876 7.72564 10.9879 7.68114L12.9955 7.235C12.8725 10.4401 10.2353 13.0008 7.00001 13.0008C3.7865 13.0008 1.16303 10.4745 1.00732 7.29959Z" fill="#CD722C" />{" "}
    </svg>
  );
}

export function OpenStatusIcon() {
  return (
    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      {" "}
      <circle cx="7.45581" cy="7" r="6.63158" fill="white" stroke="#002D5D" stroke-width="0.736842" /> <path d="M11.1573 11.7237C10.1377 12.524 8.85243 13.0013 7.45569 13.0013C6.01753 13.0013 4.69755 12.4953 3.66406 11.6516L7.18978 11.064C7.36593 11.0346 7.54595 11.0375 7.72107 11.0726L10.8354 11.6954C10.9416 11.7167 11.0496 11.7261 11.1573 11.7237Z" fill="#002D5D" />{" "}
    </svg>
  );
}

export function ClaimIcon(props) {
  return (
    <svg {...props} width="15" height="15" viewBox="0 0 14 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7.36611 10.875C7.04491 10.8774 6.73378 10.9873 6.48233 11.1872C6.23088 11.3871 6.0536 11.6654 5.97877 11.9778C5.90394 12.2902 5.93586 12.6186 6.06945 12.9107C6.20304 13.2028 6.43059 13.4418 6.71583 13.5895V15.2957H7.90901V13.6432C8.21705 13.5169 8.47174 13.2877 8.62961 12.9946C8.78749 12.7015 8.83877 12.3628 8.7747 12.0361C8.71063 11.7094 8.53519 11.4151 8.2783 11.2033C8.02142 10.9916 7.69902 10.8755 7.36611 10.875Z" fill="#1D1D11" />
      <path
        d="M12.0852 7.54091V4.9875C12.1109 3.69402 11.6231 2.44306 10.7286 1.50835C9.83414 0.573644 8.60585 0.0313007 7.3125 0C6.01915 0.0313007 4.79086 0.573644 3.8964 1.50835C3.00194 2.44306 2.51415 3.69402 2.53977 4.9875V7.54091H0.75V17.683C0.75 17.9994 0.87571 18.3029 1.09947 18.5267C1.32324 18.7504 1.62673 18.8761 1.94318 18.8761H12.6818C12.9983 18.8761 13.3018 18.7504 13.5255 18.5267C13.7493 18.3029 13.875 17.9994 13.875 17.683V7.54091H12.0852ZM3.73295 4.9875C3.70723 4.01043 4.06929 3.06289 4.74001 2.35193C5.41073 1.64096 6.33559 1.22437 7.3125 1.19318C8.28941 1.22437 9.21427 1.64096 9.88499 2.35193C10.5557 3.06289 10.9178 4.01043 10.892 4.9875V7.54091H3.73295V4.9875ZM1.94318 17.683V8.73409H12.6818V17.683H1.94318Z"
        fill="#1D1D11"
      />
    </svg>
  );
}
export function ReleaseIcon(props) {
  return (
    <svg {...props} width="15" height="15" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_501_24335)">
        <path d="M6.66673 13.967V15.5559H7.77784V14.017C8.06291 13.9027 8.30023 13.6942 8.45031 13.4262C8.6004 13.1582 8.6542 12.8469 8.60277 12.5441C8.55133 12.2413 8.39776 11.9652 8.1676 11.7618C7.93745 11.5584 7.64459 11.44 7.33775 11.4262C7.03091 11.4124 6.72859 11.504 6.48109 11.6859C6.2336 11.8678 6.05585 12.129 5.97743 12.426C5.899 12.7229 5.92462 13.0378 6.05003 13.3182C6.17544 13.5986 6.39308 13.8276 6.66673 13.967Z" fill="black" />
        <path
          d="M14.4444 1.11133C13.24 1.14048 12.0962 1.64551 11.2633 2.51593C10.4303 3.38635 9.97611 4.55126 9.99997 5.75577V8.33355H1.11108V17.778C1.11108 18.0727 1.22815 18.3553 1.43652 18.5637C1.64489 18.772 1.92751 18.8891 2.2222 18.8891H12.2222C12.5169 18.8891 12.7995 18.772 13.0079 18.5637C13.2162 18.3553 13.3333 18.0727 13.3333 17.778V8.33355H11.1111V5.75577C11.0871 4.8459 11.4243 3.96354 12.0489 3.30148C12.6735 2.63942 13.5347 2.25148 14.4444 2.22244C15.3541 2.25148 16.2154 2.63942 16.84 3.30148C17.4646 3.96354 17.8017 4.8459 17.7778 5.75577V9.55022C17.7778 9.69756 17.8363 9.83887 17.9405 9.94305C18.0447 10.0472 18.186 10.1058 18.3333 10.1058C18.4806 10.1058 18.622 10.0472 18.7261 9.94305C18.8303 9.83887 18.8889 9.69756 18.8889 9.55022V5.75577C18.9127 4.55126 18.4585 3.38635 17.6256 2.51593C16.7926 1.64551 15.6488 1.14048 14.4444 1.11133ZM12.2222 9.44466V17.778H2.2222V9.44466H12.2222Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_501_24335">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Cherrywork(props) {
  return (
    <svg {...props} width="31" height="36" viewBox="0 0 31 36" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <rect y="0.936035" width="31" height="35" fill="url(#pattern0)" />
      <defs>
        <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
          <use xlinkHref="#image0_3305_5353" transform="translate(0 -0.00183195) scale(0.00123305 0.00109213)" />
        </pattern>
        <image
          id="image0_3305_5353"
          width="811"
          height="919"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
}

export const FilterViewDelete = (props) => {
  return (
    <svg {...props} width="14" height="17" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5.47795 16.375C5.82994 16.375 6.11521 16.0996 6.11521 15.7598C6.11521 15.4199 5.82994 15.1445 5.47795 15.1445H2.67402C1.9713 15.1445 1.39951 14.5925 1.39951 13.9141L1.39951 3.08594C1.39951 2.4075 1.9713 1.85547 2.67402 1.85547L10.5088 1.85547C11.2115 1.85547 11.7833 2.4075 11.7833 3.08594V8.96143C11.7833 9.30125 12.0686 9.57666 12.4206 9.57666C12.7724 9.57666 13.0578 9.30125 13.0578 8.96143V3.08594C13.0578 1.72894 11.9142 0.625 10.5088 0.625L2.67402 0.625C1.26845 0.625 0.125 1.72894 0.125 3.08594L0.125 13.9141C0.125 15.2711 1.26845 16.375 2.67402 16.375H5.47795Z"
        fill="#1D1D11"
      />
      <path d="M3.3078 4.31641H9.87154C10.2234 4.31641 10.5088 4.59182 10.5088 4.93164C10.5088 5.27146 10.2234 5.54688 9.87154 5.54688H3.3078C2.95581 5.54688 2.67054 5.27146 2.67054 4.93164C2.67054 4.59182 2.95581 4.31641 3.3078 4.31641Z" fill="#1D1D11" />
      <path d="M9.87154 6.77734C10.2234 6.77734 10.5088 7.05276 10.5088 7.39258C10.5088 7.7324 10.2234 8.00781 9.87154 8.00781H3.3078C2.95581 8.00781 2.67054 7.7324 2.67054 7.39258C2.67054 7.05276 2.95581 6.77734 3.3078 6.77734H9.87154Z" fill="#1D1D11" />
      <path d="M2.67054 9.85352C2.67054 9.51369 2.95581 9.23828 3.3078 9.23828H6.65688C7.00886 9.23828 7.29413 9.51369 7.29413 9.85352C7.29413 10.1933 7.00886 10.4688 6.65688 10.4688H3.3078C2.95581 10.4688 2.67054 10.1933 2.67054 9.85352Z" fill="#1D1D11" />
      <path d="M12.5 17V12.2073H12.8635V11.8437H11.9281H11.5V11H10H9V11.8437H7.64704V12.2073H8V17H12.5Z" fill="#1D1D11" />
    </svg>
  );
};

/* ################################################ Side Nav Icons - Start ############################################################### */

export function HomeIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.1388 6.7452L5.04297 11.3171V19.0412C5.04297 19.9498 5.77947 20.6863 6.68799 20.6863H17.4518C18.3603 20.6863 19.0968 19.9498 19.0968 19.0412V10.7315L13.0383 6.69248C12.458 6.3056 11.6967 6.32672 11.1388 6.7452Z" fill="#FFE6CF" />
      <path d="M12.0685 6.86011L3.66259 13.4594C2.98425 13.9919 2.00263 13.8737 1.47008 13.1954C0.937528 12.5171 1.05571 11.5355 1.73405 11.0029L11.1033 3.64732C11.3898 3.42238 11.7304 3.31354 12.0685 3.31397C12.4065 3.31354 12.7471 3.42238 13.0337 3.64732L22.4029 11.0029C23.0812 11.5355 23.1994 12.5171 22.6668 13.1954C22.1343 13.8737 21.1527 13.9919 20.4743 13.4594L12.0685 6.86011Z" fill="#CD4968" />
      <path d="M9.72656 13.157C9.72656 12.2485 10.4631 11.512 11.3716 11.512H12.7661C13.6747 11.512 14.4112 12.2485 14.4112 13.157V16.5452C14.4112 16.9995 14.0429 17.3677 13.5887 17.3677H10.5491C10.0948 17.3677 9.72656 16.9995 9.72656 16.5452V13.157Z" fill="#EB7D18" />
    </svg>
  );
}

export const SubstitutingFor = (props) => {
  return (
    <svg {...props} width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        color={props?.style?.color}
        className={props?.className}
        d="M6.50027 0.593966C7.16688 0.593966 7.73373 0.827532 8.20082 1.29467C8.66791 1.7618 8.90145 2.32865 8.90145 2.99523C8.90145 3.58707 8.7147 4.10468 8.3412 4.54808C7.96769 4.99148 7.49846 5.26308 6.9335 5.36288L6.9335 6.7696L9.29503 6.7696C9.58978 6.7696 9.84209 6.87454 10.052 7.08443C10.2619 7.29432 10.3668 7.54664 10.3668 7.84138L10.3668 8.63497L11.5989 8.63497C11.7507 8.63497 11.878 8.68632 11.9807 8.78903C12.0834 8.89174 12.1348 9.01902 12.1348 9.17087L12.1348 12.4991C12.1348 12.6537 12.0834 12.7821 11.9807 12.8842C11.878 12.9864 11.7507 13.0375 11.5989 13.0375L8.27067 13.0375C8.11607 13.0375 7.98768 12.9861 7.8855 12.8834C7.78332 12.7807 7.73223 12.6534 7.73223 12.5016L7.73223 9.1734C7.73223 9.0188 7.78359 8.89041 7.8863 8.78823C7.98902 8.68605 8.1163 8.63497 8.26813 8.63497L9.50018 8.63497L9.50018 7.84138C9.50018 7.78155 9.48095 7.7324 9.44248 7.69393C9.40402 7.65547 9.35487 7.63623 9.29503 7.63623L3.50533 7.63623C3.4455 7.63623 3.39635 7.65547 3.35788 7.69393C3.31941 7.7324 3.30018 7.78155 3.30018 7.84138L3.30018 8.676C3.86514 8.77087 4.33437 9.04 4.70787 9.4834C5.08138 9.9268 5.26813 10.4444 5.26813 11.0362C5.26813 11.7028 5.03455 12.2697 4.5674 12.7368C4.10025 13.2039 3.53338 13.4375 2.86676 13.4375C2.20016 13.4375 1.63332 13.2039 1.16623 12.7368C0.699143 12.2697 0.465598 11.7028 0.465598 11.0362C0.465598 10.4444 0.652349 9.9268 1.02585 9.4834C1.39935 9.04 1.86858 8.77087 2.43353 8.676L2.43353 7.84138C2.43353 7.54664 2.53848 7.29432 2.74838 7.08443C2.95827 6.87454 3.21059 6.7696 3.50533 6.7696L6.06687 6.7696L6.06687 5.35548C5.50191 5.26062 5.03268 4.99148 4.65917 4.54808C4.28567 4.10468 4.09892 3.58707 4.09892 2.99523C4.09892 2.32865 4.33249 1.7618 4.79965 1.29467C5.26679 0.827532 5.83367 0.593966 6.50027 0.593966ZM2.86315 9.50162C2.43827 9.50162 2.0769 9.65179 1.77903 9.95213C1.48117 10.2525 1.33223 10.6151 1.33223 11.0399C1.33223 11.4648 1.4824 11.8262 1.78275 12.1241C2.08309 12.4219 2.4457 12.5709 2.87058 12.5709C3.29546 12.5709 3.65683 12.4207 3.95468 12.1203C4.25255 11.82 4.40148 11.4574 4.40148 11.0325C4.40148 10.6076 4.25131 10.2463 3.95097 9.94842C3.65062 9.65055 3.28802 9.50162 2.86315 9.50162ZM11.2681 9.50162L8.59888 9.50162L8.59888 12.1709L11.2681 12.1709L11.2681 9.50162ZM6.49647 1.46061C6.07159 1.46061 5.71022 1.61079 5.41235 1.91113C5.11448 2.21147 4.96555 2.57407 4.96555 2.99895C4.96555 3.42383 5.11572 3.7852 5.41607 4.08307C5.71641 4.38093 6.07902 4.52987 6.5039 4.52987C6.92878 4.52987 7.29015 4.37969 7.58802 4.07935C7.88588 3.779 8.03482 3.41639 8.03482 2.99152C8.03482 2.56665 7.88464 2.20528 7.5843 1.90742C7.28396 1.60955 6.92134 1.46061 6.49647 1.46061Z"
        fill={props?.style?.color ?? "#757575"}
      />
    </svg>
  );
};

export function HomeIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.1388 6.7452L5.04297 11.3171V19.0412C5.04297 19.9498 5.77947 20.6863 6.68799 20.6863H17.4518C18.3603 20.6863 19.0968 19.9498 19.0968 19.0412V10.7315L13.0383 6.69248C12.458 6.3056 11.6967 6.32672 11.1388 6.7452Z" fill="#B7C2D2" />
      <path d="M12.0685 6.86011L3.66259 13.4594C2.98425 13.9919 2.00263 13.8737 1.47008 13.1954C0.937528 12.5171 1.05571 11.5355 1.73405 11.0029L11.1033 3.64732C11.3898 3.42238 11.7304 3.31354 12.0685 3.31397C12.4065 3.31354 12.7471 3.42238 13.0337 3.64732L22.4029 11.0029C23.0812 11.5355 23.1994 12.5171 22.6668 13.1954C22.1343 13.8737 21.1527 13.9919 20.4743 13.4594L12.0685 6.86011Z" fill="#64748B" />
      <path d="M9.72656 13.157C9.72656 12.2485 10.4631 11.512 11.3716 11.512H12.7661C13.6747 11.512 14.4112 12.2485 14.4112 13.157V16.5452C14.4112 16.9995 14.0429 17.3677 13.5887 17.3677H10.5491C10.0948 17.3677 9.72656 16.9995 9.72656 16.5452V13.157Z" fill="#64748B" />
    </svg>
  );
}

export function WorkspaceIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M5.825 20.625C4.85833 20.625 4.04167 20.2917 3.375 19.625C2.70833 18.9583 2.375 18.1417 2.375 17.175C2.375 16.225 2.70833 15.4167 3.375 14.75C4.04167 14.0833 4.85833 13.75 5.825 13.75C6.775 13.75 7.58333 14.0833 8.25 14.75C8.91667 15.4167 9.25 16.225 9.25 17.175C9.25 18.1417 8.91667 18.9583 8.25 19.625C7.58333 20.2917 6.775 20.625 5.825 20.625Z" fill="#3D85EE" />
      <path d="M15.7734 19.625C16.4401 20.2917 17.2484 20.625 18.1984 20.625C19.1484 20.625 19.9568 20.2917 20.6234 19.625C21.2901 18.9583 21.6234 18.1417 21.6234 17.175C21.6234 16.225 21.2901 15.4167 20.6234 14.75C19.9568 14.0833 19.1484 13.75 18.1984 13.75C17.2484 13.75 16.4401 14.0833 15.7734 14.75C15.1068 15.4167 14.7734 16.225 14.7734 17.175C14.7734 18.1417 15.1068 18.9583 15.7734 19.625Z" fill="#CD4968" />
      <path d="M9.58672 9.225C10.2617 9.89167 11.0742 10.225 12.0242 10.225C12.9742 10.225 13.7826 9.89167 14.4492 9.225C15.1159 8.55833 15.4492 7.75 15.4492 6.8C15.4492 5.85 15.1159 5.04167 14.4492 4.375C13.7826 3.70833 12.9742 3.375 12.0242 3.375C11.0742 3.375 10.2617 3.70833 9.58672 4.375C8.91172 5.04167 8.57422 5.85 8.57422 6.8C8.57422 7.75 8.91172 8.55833 9.58672 9.225Z" fill="#ED872A" />
    </svg>
  );
}

export function WorkspaceIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M5.69761 20.9473C4.66156 20.9473 3.78628 20.59 3.07177 19.8755C2.35726 19.161 2 18.2857 2 17.2497C2 16.2315 2.35726 15.3651 3.07177 14.6506C3.78628 13.9361 4.66156 13.5789 5.69761 13.5789C6.71579 13.5789 7.58214 13.9361 8.29665 14.6506C9.01116 15.3651 9.36842 16.2315 9.36842 17.2497C9.36842 18.2857 9.01116 19.161 8.29665 19.8755C7.58214 20.59 6.71579 20.9473 5.69761 20.9473Z" fill="#64748B" />
      <path d="M15.7046 19.8755C16.4217 20.59 17.2912 20.9473 18.3131 20.9473C19.335 20.9473 20.2045 20.59 20.9216 19.8755C21.6388 19.161 21.9973 18.2857 21.9973 17.2497C21.9973 16.2315 21.6388 15.3651 20.9216 14.6506C20.2045 13.9361 19.335 13.5789 18.3131 13.5789C17.2912 13.5789 16.4217 13.9361 15.7046 14.6506C14.9875 15.3651 14.6289 16.2315 14.6289 17.2497C14.6289 18.2857 14.9875 19.161 15.7046 19.8755Z" fill="#64748B" />
      <path d="M9.40157 9.34547C10.125 10.0626 10.9958 10.4212 12.014 10.4212C13.0322 10.4212 13.8985 10.0626 14.6131 9.34547C15.3276 8.62835 15.6848 7.75884 15.6848 6.73694C15.6848 5.71505 15.3276 4.84554 14.6131 4.12842C13.8985 3.41129 13.0322 3.05273 12.014 3.05273C10.9958 3.05273 10.125 3.41129 9.40157 4.12842C8.67813 4.84554 8.31641 5.71505 8.31641 6.73694C8.31641 7.75884 8.67813 8.62835 9.40157 9.34547Z" fill="#B7C2D2" />
    </svg>
  );
}

export function AnalyticsIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21.1703 10.1667L15.67 15.6671L9.25298 11.0835L2.83594 17.5005" stroke="#748399" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M4.39089 10.2343L4.70004 10.9233L5.02851 10.2343L5.70477 9.90898L5.02851 9.60277L4.70004 8.91382L4.39089 9.60277L3.69531 9.90898L4.39089 10.2343Z" fill="#053479" />
      <path d="M14.4478 7.54038L15.0837 8.91393L15.7196 7.54038L17.0931 6.90447L15.7196 6.26857L15.0837 4.89502L14.4478 6.26857L13.0742 6.90447L14.4478 7.54038Z" fill="#053479" />
      <circle cx="2.85396" cy="17.2509" r="1.85396" fill="#E07B1F" />
      <circle cx="15.6694" cy="15.6669" r="1.83344" fill="#3D85EE" />
      <circle cx="21.1469" cy="10.1462" r="1.85396" fill="#CB4061" />
      <circle cx="9.25016" cy="11.2836" r="2.75016" fill="#3D85EE" />
    </svg>
  );
}

export function AnalyticsIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21.1703 10.1667L15.67 15.6671L9.25298 11.0835L2.83594 17.5005" stroke="#748399" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M4.39089 10.2343L4.70004 10.9233L5.02851 10.2343L5.70477 9.90898L5.02851 9.60277L4.70004 8.91382L4.39089 9.60277L3.69531 9.90898L4.39089 10.2343Z" fill="#B7C2D2" />
      <path d="M14.4478 7.54038L15.0837 8.91393L15.7196 7.54038L17.0931 6.90447L15.7196 6.26857L15.0837 4.89502L14.4478 6.26857L13.0742 6.90447L14.4478 7.54038Z" fill="#B7C2D2" />
      <circle cx="2.85396" cy="17.2509" r="1.85396" fill="#64748B" />
      <circle cx="15.6694" cy="15.6669" r="1.83344" fill="#B7C2D2" />
      <circle cx="21.1469" cy="10.1462" r="1.85396" fill="#64748B" />
      <circle cx="9.25016" cy="11.2836" r="2.75016" fill="#B7C2D2" />
    </svg>
  );
}

export function AdminConsoleIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M18.6355 6.50539V11.2308C12.1097 9.69231 8.26352 15.0769 12.1097 22C6.86902 22 2.87891 17.4234 2.87891 12.1827V5.52043C2.87891 5.29751 3.01463 5.09704 3.22161 5.01425L10.0144 2.29711C10.4913 2.10638 11.0232 2.10638 11.5 2.29711L17.3783 4.64844C18.1376 4.95217 18.6355 5.68758 18.6355 6.50539Z" fill="#3D85EE" />
      <path d="M19.4625 19.5972C19.4625 21.0475 17.5543 21.1728 16.3111 21.1728C15.068 21.1728 13.1598 21.0475 13.1598 19.5972C13.1598 18.1468 15.068 16.9711 16.3111 16.9711C17.5543 16.9711 19.4625 18.1468 19.4625 19.5972Z" fill="#ED872A" />
      <path d="M18.412 15.9206C18.412 17.0809 17.4714 18.0215 16.3111 18.0215C15.1509 18.0215 14.2103 17.0809 14.2103 15.9206C14.2103 14.7603 15.1509 13.8197 16.3111 13.8197C17.4714 13.8197 18.412 14.7603 18.412 15.9206Z" fill="#CD4968" />
      <path
        d="M16.5738 17.2337C17.0289 17.2337 17.4141 17.0717 17.7292 16.7478C18.0444 16.4239 18.2019 16.0432 18.2019 15.6055C18.2019 15.1503 18.04 14.7651 17.7161 14.45C17.3922 14.1349 17.0114 13.9773 16.5738 13.9773C16.1186 13.9773 15.729 14.1349 15.4051 14.45C15.0812 14.7651 14.9193 15.1415 14.9193 15.5792C14.9193 16.0344 15.0812 16.4239 15.4051 16.7478C15.729 17.0717 16.1186 17.2337 16.5738 17.2337ZM16.5475 20.49C17.1252 20.49 17.6505 20.3675 18.1231 20.1224C18.5958 19.8773 18.9898 19.5359 19.3049 19.0982C18.8672 18.8531 18.4208 18.6693 17.9656 18.5467C17.5104 18.4242 17.0377 18.3629 16.5475 18.3629C16.0748 18.3629 15.6065 18.4242 15.1425 18.5467C14.6786 18.6693 14.2365 18.8531 13.8163 19.0982C14.1315 19.5359 14.521 19.8773 14.985 20.1224C15.4489 20.3675 15.9697 20.49 16.5475 20.49ZM16.6 21.7768C15.357 21.7768 14.2978 21.3348 13.4224 20.4506C12.5471 19.5665 12.1094 18.5205 12.1094 17.3124C12.1094 16.0519 12.5471 14.9796 13.4224 14.0955C14.2978 13.2113 15.3657 12.7693 16.6263 12.7693C17.8518 12.7693 18.9066 13.2113 19.7907 14.0955C20.6748 14.9796 21.1169 16.0519 21.1169 17.3124C21.1169 18.5205 20.6748 19.5665 19.7907 20.4506C18.9066 21.3348 17.843 21.7768 16.6 21.7768Z"
        fill="#FFE6CF"
      />
    </svg>
  );
}

export function AdminConsoleIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M18.6355 6.50539V11.2308C12.1097 9.69231 8.26352 15.0769 12.1097 22C6.86902 22 2.87891 17.4234 2.87891 12.1827V5.52043C2.87891 5.29751 3.01463 5.09704 3.22161 5.01425L10.0144 2.29711C10.4913 2.10638 11.0232 2.10638 11.5 2.29711L17.3783 4.64844C18.1376 4.95217 18.6355 5.68758 18.6355 6.50539Z" fill="#B7C2D2" />
      <path d="M19.4664 19.5972C19.4664 21.0475 17.5582 21.1728 16.315 21.1728C15.0719 21.1728 13.1637 21.0475 13.1637 19.5972C13.1637 18.1468 15.0719 16.9711 16.315 16.9711C17.5582 16.9711 19.4664 18.1468 19.4664 19.5972Z" fill="#4E596F" />
      <path d="M18.4159 15.9206C18.4159 17.0809 17.4753 18.0215 16.315 18.0215C15.1548 18.0215 14.2142 17.0809 14.2142 15.9206C14.2142 14.7603 15.1548 13.8197 16.315 13.8197C17.4753 13.8197 18.4159 14.7603 18.4159 15.9206Z" fill="#4E596F" />
      <path
        d="M16.5777 17.2337C17.0328 17.2337 17.418 17.0717 17.7331 16.7478C18.0483 16.4239 18.2058 16.0432 18.2058 15.6055C18.2058 15.1503 18.0439 14.7651 17.72 14.45C17.3961 14.1349 17.0153 13.9773 16.5777 13.9773C16.1225 13.9773 15.7329 14.1349 15.409 14.45C15.0852 14.7651 14.9232 15.1415 14.9232 15.5792C14.9232 16.0344 15.0852 16.4239 15.409 16.7478C15.7329 17.0717 16.1225 17.2337 16.5777 17.2337ZM16.5514 20.49C17.1291 20.49 17.6544 20.3675 18.1271 20.1224C18.5998 19.8773 18.9937 19.5359 19.3088 19.0982C18.8711 18.8531 18.4247 18.6693 17.9695 18.5467C17.5143 18.4242 17.0416 18.3629 16.5514 18.3629C16.0787 18.3629 15.6104 18.4242 15.1464 18.5467C14.6825 18.6693 14.2404 18.8531 13.8202 19.0982C14.1354 19.5359 14.5249 19.8773 14.9889 20.1224C15.4528 20.3675 15.9737 20.49 16.5514 20.49ZM16.6039 21.7768C15.3609 21.7768 14.3017 21.3348 13.4263 20.4506C12.551 19.5665 12.1133 18.5205 12.1133 17.3124C12.1133 16.0519 12.551 14.9796 13.4263 14.0955C14.3017 13.2113 15.3696 12.7693 16.6302 12.7693C17.8557 12.7693 18.9105 13.2113 19.7946 14.0955C20.6788 14.9796 21.1208 16.0519 21.1208 17.3124C21.1208 18.5205 20.6788 19.5665 19.7946 20.4506C18.9105 21.3348 17.8469 21.7768 16.6039 21.7768Z"
        fill="#B7C2D2"
      />
    </svg>
  );
}

export function NotificationsIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.8719 3.19994C12.6626 2.85553 11.3812 2.85553 10.1719 3.19994C10.4619 2.45994 11.1819 1.93994 12.0219 1.93994C12.8619 1.93994 13.5819 2.45994 13.8719 3.19994Z" fill="#3D85EE" />
      <path d="M15.0195 19.0601C15.0195 20.7101 13.6695 22.0601 12.0195 22.0601C11.1995 22.0601 10.4395 21.7201 9.89953 21.1801C9.33769 20.6174 9.02132 19.8552 9.01953 19.0601" fill="#ED872A" />
      <path d="M12.0186 2.90991C8.70862 2.90991 6.01862 5.59991 6.01862 8.90991V11.7999C6.01862 12.4099 5.75862 13.3399 5.44862 13.8599L4.29862 15.7699C3.58862 16.9499 4.07862 18.2599 5.37862 18.6999C9.68862 20.1399 14.3386 20.1399 18.6486 18.6999C19.8586 18.2999 20.3886 16.8699 19.7286 15.7699L18.5786 13.8599C18.2786 13.3399 18.0186 12.4099 18.0186 11.7999V8.90991C18.0186 5.60991 15.3186 2.90991 12.0186 2.90991Z" fill="#FFE6CF" />
    </svg>
  );
}

export function NotificationsIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.8719 3.19994C12.6626 2.85553 11.3812 2.85553 10.1719 3.19994C10.4619 2.45994 11.1819 1.93994 12.0219 1.93994C12.8619 1.93994 13.5819 2.45994 13.8719 3.19994Z" fill="#64748B" />
      <path d="M15.0195 19.0601C15.0195 20.7101 13.6695 22.0601 12.0195 22.0601C11.1995 22.0601 10.4395 21.7201 9.89953 21.1801C9.33769 20.6174 9.02132 19.8552 9.01953 19.0601" fill="#64748B" />
      <path d="M12.0186 2.90991C8.70862 2.90991 6.01862 5.59991 6.01862 8.90991V11.7999C6.01862 12.4099 5.75862 13.3399 5.44862 13.8599L4.29862 15.7699C3.58862 16.9499 4.07862 18.2599 5.37862 18.6999C9.68862 20.1399 14.3386 20.1399 18.6486 18.6999C19.8586 18.2999 20.3886 16.8699 19.7286 15.7699L18.5786 13.8599C18.2786 13.3399 18.0186 12.4099 18.0186 11.7999V8.90991C18.0186 5.60991 15.3186 2.90991 12.0186 2.90991Z" fill="#B7C2D2" />
    </svg>
  );
}

export function SettingsIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2 12.8799V11.1199C2 10.0799 2.85 9.21994 3.9 9.21994C5.71 9.21994 6.45 7.93994 5.54 6.36994C5.02 5.46994 5.33 4.29994 6.24 3.77994L7.97 2.78994C8.76 2.31994 9.78 2.59994 10.25 3.38994L10.36 3.57994C11.26 5.14994 12.74 5.14994 13.65 3.57994L13.76 3.38994C14.23 2.59994 15.25 2.31994 16.04 2.78994L17.77 3.77994C18.68 4.29994 18.99 5.46994 18.47 6.36994C17.56 7.93994 18.3 9.21994 20.11 9.21994C21.15 9.21994 22.01 10.0699 22.01 11.1199V12.8799C22.01 13.9199 21.16 14.7799 20.11 14.7799C18.3 14.7799 17.56 16.0599 18.47 17.6299C18.99 18.5399 18.68 19.6999 17.77 20.2199L16.04 21.2099C15.25 21.6799 14.23 21.3999 13.76 20.6099L13.65 20.4199C12.75 18.8499 11.27 18.8499 10.36 20.4199L10.25 20.6099C9.78 21.3999 8.76 21.6799 7.97 21.2099L6.24 20.2199C5.8041 19.969 5.48558 19.5553 5.35435 19.0697C5.22311 18.5841 5.28988 18.0663 5.54 17.6299C6.45 16.0599 5.71 14.7799 3.9 14.7799C2.85 14.7799 2 13.9199 2 12.8799Z"
        fill="#FFE6CF"
      />
      <path d="M12 15C12.7956 15 13.5587 14.6839 14.1213 14.1213C14.6839 13.5587 15 12.7956 15 12C15 11.2044 14.6839 10.4413 14.1213 9.87868C13.5587 9.31607 12.7956 9 12 9C11.2044 9 10.4413 9.31607 9.87868 9.87868C9.31607 10.4413 9 11.2044 9 12C9 12.7956 9.31607 13.5587 9.87868 14.1213C10.4413 14.6839 11.2044 15 12 15Z" fill="#CB4061" />
    </svg>
  );
}

export function SettingsIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2 12.8799V11.1199C2 10.0799 2.85 9.21994 3.9 9.21994C5.71 9.21994 6.45 7.93994 5.54 6.36994C5.02 5.46994 5.33 4.29994 6.24 3.77994L7.97 2.78994C8.76 2.31994 9.78 2.59994 10.25 3.38994L10.36 3.57994C11.26 5.14994 12.74 5.14994 13.65 3.57994L13.76 3.38994C14.23 2.59994 15.25 2.31994 16.04 2.78994L17.77 3.77994C18.68 4.29994 18.99 5.46994 18.47 6.36994C17.56 7.93994 18.3 9.21994 20.11 9.21994C21.15 9.21994 22.01 10.0699 22.01 11.1199V12.8799C22.01 13.9199 21.16 14.7799 20.11 14.7799C18.3 14.7799 17.56 16.0599 18.47 17.6299C18.99 18.5399 18.68 19.6999 17.77 20.2199L16.04 21.2099C15.25 21.6799 14.23 21.3999 13.76 20.6099L13.65 20.4199C12.75 18.8499 11.27 18.8499 10.36 20.4199L10.25 20.6099C9.78 21.3999 8.76 21.6799 7.97 21.2099L6.24 20.2199C5.8041 19.969 5.48558 19.5553 5.35435 19.0697C5.22311 18.5841 5.28988 18.0663 5.54 17.6299C6.45 16.0599 5.71 14.7799 3.9 14.7799C2.85 14.7799 2 13.9199 2 12.8799Z"
        fill="#B7C2D2"
      />
      <path d="M12 15C12.7956 15 13.5587 14.6839 14.1213 14.1213C14.6839 13.5587 15 12.7956 15 12C15 11.2044 14.6839 10.4413 14.1213 9.87868C13.5587 9.31607 12.7956 9 12 9C11.2044 9 10.4413 9.31607 9.87868 9.87868C9.31607 10.4413 9 11.2044 9 12C9 12.7956 9.31607 13.5587 9.87868 14.1213C10.4413 14.6839 11.2044 15 12 15Z" fill="#4E596F" />
    </svg>
  );
}

export function CreateTaskIconActive(props) {
  return (
    <svg {...props} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12H20M12 20V4" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  );
}

export function CreateTaskIconPassive(props) {
  return (
    <svg {...props} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12H20M12 20V4" stroke="#B7C2D2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  );
}

export function MenuTabIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 18H20C20.55 18 21 17.55 21 17C21 16.45 20.55 16 20 16H4C3.45 16 3 16.45 3 17C3 17.55 3.45 18 4 18ZM4 13H20C20.55 13 21 12.55 21 12C21 11.45 20.55 11 20 11H4C3.45 11 3 11.45 3 12C3 12.55 3.45 13 4 13ZM3 7C3 7.55 3.45 8 4 8H20C20.55 8 21 7.55 21 7C21 6.45 20.55 6 20 6H4C3.45 6 3 6.45 3 7Z" fill="#FFFFFF" />
    </svg>
  );
}

export function MenuTabIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 18H20C20.55 18 21 17.55 21 17C21 16.45 20.55 16 20 16H4C3.45 16 3 16.45 3 17C3 17.55 3.45 18 4 18ZM4 13H20C20.55 13 21 12.55 21 12C21 11.45 20.55 11 20 11H4C3.45 11 3 11.45 3 12C3 12.55 3.45 13 4 13ZM3 7C3 7.55 3.45 8 4 8H20C20.55 8 21 7.55 21 7C21 6.45 20.55 6 20 6H4C3.45 6 3 6.45 3 7Z" fill="#B7C2D2" />
    </svg>
  );
}

export function SearchIconActive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16.2108 14.6359C17.3001 13.1494 17.788 11.3064 17.5769 9.47568C17.3657 7.64493 16.4711 5.9614 15.072 4.76191C13.6729  3.56243 11.8725 2.93545 10.031 3.0064C8.18942 3.07736 6.44259 3.84102 5.13994 5.14461C3.83728 6.4482 3.07487 8.19558 3.00523 10.0372C2.93559 11.8787 3.56386 13.6787 4.76434 15.0769C5.96483 16.4752 7.649 17.3686 9.47991 17.5784C11.3108 17.7883 13.1534 17.299 14.6391 16.2086H14.638C14.6718 16.2536 14.7078 16.2964 14.7483 16.338L19.0795 20.6693C19.2905 20.8804 19.5766 20.999 19.875 20.9991C20.1735 20.9992 20.4597 20.8808 20.6708 20.6698C20.8819 20.4589 21.0006 20.1727 21.0007 19.8743C21.0008 19.5759 20.8823 19.2896 20.6714 19.0785L16.3401 14.7473C16.2999 14.7066 16.2567 14.6689 16.2108 14.6348V14.6359ZM16.501 10.3114C16.501 11.1239 16.341 11.9285 16.03 12.6792C15.7191 13.4299 15.2633 14.1121 14.6887 14.6866C14.1142 15.2612 13.4321 15.7169 12.6814 16.0279C11.9307 16.3388 11.1261 16.4989 10.3135 16.4989C9.50096 16.4989 8.69636 16.3388 7.94566 16.0279C7.19495 15.7169 6.51285 15.2612 5.93829 14.6866C5.36372 14.1121 4.90796 13.4299 4.59701 12.6792C4.28605 11.9285 4.12601 11.1239 4.12601 10.3114C4.12601 8.67037 4.77791 7.09655 5.93829 5.93617C7.09867 4.77579 8.67248 4.12389 10.3135 4.12389C11.9545 4.12389 13.5284 4.77579 14.6887 5.93617C15.8491 7.09655 16.501 8.67037 16.501 10.3114Z"
        fill="#FFFFFF"
      />
    </svg>
  );
}

export function SearchIconPassive() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16.2108 14.6359C17.3001 13.1494 17.788 11.3064 17.5769 9.47568C17.3657 7.64493 16.4711 5.9614 15.072 4.76191C13.6729  3.56243 11.8725 2.93545 10.031 3.0064C8.18942 3.07736 6.44259 3.84102 5.13994 5.14461C3.83728 6.4482 3.07487 8.19558 3.00523 10.0372C2.93559 11.8787 3.56386 13.6787 4.76434 15.0769C5.96483 16.4752 7.649 17.3686 9.47991 17.5784C11.3108 17.7883 13.1534 17.299 14.6391 16.2086H14.638C14.6718 16.2536 14.7078 16.2964 14.7483 16.338L19.0795 20.6693C19.2905 20.8804 19.5766 20.999 19.875 20.9991C20.1735 20.9992 20.4597 20.8808 20.6708 20.6698C20.8819 20.4589 21.0006 20.1727 21.0007 19.8743C21.0008 19.5759 20.8823 19.2896 20.6714 19.0785L16.3401 14.7473C16.2999 14.7066 16.2567 14.6689 16.2108 14.6348V14.6359ZM16.501 10.3114C16.501 11.1239 16.341 11.9285 16.03 12.6792C15.7191 13.4299 15.2633 14.1121 14.6887 14.6866C14.1142 15.2612 13.4321 15.7169 12.6814 16.0279C11.9307 16.3388 11.1261 16.4989 10.3135 16.4989C9.50096 16.4989 8.69636 16.3388 7.94566 16.0279C7.19495 15.7169 6.51285 15.2612 5.93829 14.6866C5.36372 14.1121 4.90796 13.4299 4.59701 12.6792C4.28605 11.9285 4.12601 11.1239 4.12601 10.3114C4.12601 8.67037 4.77791 7.09655 5.93829 5.93617C7.09867 4.77579 8.67248 4.12389 10.3135 4.12389C11.9545 4.12389 13.5284 4.77579 14.6887 5.93617C15.8491 7.09655 16.501 8.67037 16.501 10.3114Z"
        fill="#B7C2D2"
      />
    </svg>
  );
}
/* ################################################ Side Nav Icons - End ############################################################### */

export const MySubstitute = (props) => {
  return (
    <svg {...props} width="17" height="14" viewBox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path color={props?.style?.color} className={props?.className} d="M5.49965 13.2121L4.72563 12.4461L7.12951 10.0422H0.708008V8.95894H7.12951L4.72563 6.55506L5.49965 5.78906L9.21117 9.50058L5.49965 13.2121ZM11.4997 8.2121L7.78813 4.50058L11.4997 0.789062L12.2737 1.55506L9.8698 3.95894H16.2913V5.04223H9.8698L12.2737 7.44611L11.4997 8.2121Z" fill={props?.style?.color ?? "#3026B9"} />
    </svg>
  );
};

export function Ariba(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.2046 13.3412L8.58445 3H9.82817C10.0807 3 10.277 3.1209 10.3799 3.32549L16 13.6667H14.7563C14.5319 13.6667 14.3074 13.5458 14.2046 13.3412ZM3.65634 13.6667H4.90006C5.15254 13.6667 5.34892 13.5458 5.45178 13.3412L6.99474 10.5234L6.19988 9.03546L3.65634 13.6667ZM1.24372 13.6667C1.4962 13.6667 1.69258 13.5458 1.79544 13.3412L6.17183 5.29701L10.5482 13.3412C10.6511 13.5365 10.8475 13.6667 11.0999 13.6667H12.3437L6.72355 3.32549C6.62069 3.13019 6.42431 3 6.17183 3C5.9474 3 5.72297 3.1209 5.6201 3.32549L0 13.6667H1.24372Z" fill="#FBBA00" />
    </svg>
  );
}

export function Concur(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7.66122 10.1554C6.48804 10.1554 5.51038 9.17864 5.51038 8.00655C5.51038 6.83446 6.44893 5.85771 7.66122 5.85771C8.24782 5.85771 8.7953 6.07911 9.16029 6.4698L10.2683 5.36283C9.59046 4.68562 8.67798 4.29492 7.63515 4.29492C5.58859 4.29492 3.93311 5.94887 3.93311 7.99353C3.93311 10.0382 5.58859 11.6921 7.63515 11.6921C8.63888 11.6921 9.59046 11.2754 10.2683 10.6242L9.16029 9.51725C8.76923 9.89492 8.24782 10.1554 7.66122 10.1554Z" fill="#F0AB00" />
      <path d="M10.7764 7.06885C10.255 7.06885 9.83789 7.48559 9.83789 8.00652C9.83789 8.52745 10.255 8.9442 10.7764 8.9442C11.2979 8.9442 11.715 8.52745 11.715 8.00652C11.728 7.49862 11.2979 7.06885 10.7764 7.06885Z" fill="#F0AB00" />
      <path d="M1 15H15V1H1V15ZM2.56425 13.4112V2.56279H13.4227V7.58977V8.37116V13.3981C13.4097 13.4112 2.56425 13.4112 2.56425 13.4112Z" fill="#F0AB00" />
    </svg>
  );
}

export function DocuSign(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_227_22)">
        <path d="M16 0H0V16H16V0Z" fill="#D5FF00" />
        <path d="M14.1248 12.374H1.87549V14.1243H14.1248V12.374Z" fill="black" />
        <path d="M8.87518 10.4245H7.12584L7.12451 1.87537H8.87518V10.4245Z" fill="black" />
        <path d="M7.99958 11.5633L3.34424 6.90641L4.59357 5.65662L7.99958 9.06235L11.4069 5.65662L12.6562 6.90641L7.99958 11.5633Z" fill="black" />
      </g>
      <defs>
        <clipPath id="clip0_227_22">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Outlook(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.7089 1H5.46702C5.28851 1 5.11731 1.07396 4.99109 1.20561C4.86486 1.33726 4.79395 1.51582 4.79395 1.702V2.5L9.43933 4L14.382 2.5V1.702C14.382 1.51582 14.311 1.33726 14.1848 1.20561C14.0586 1.07396 13.8874 1 13.7089 1Z" fill="#0364B8" />
      <path
        d="M15.1728 8.70247C15.243 8.47227 15.299 8.23763 15.3406 7.99997C15.3405 7.94159 15.3257 7.88424 15.2977 7.83369C15.2697 7.78314 15.2294 7.74117 15.1809 7.71197L15.1747 7.70797L15.1728 7.70697L9.97704 4.61997C9.95462 4.6048 9.93142 4.59095 9.90752 4.57847C9.80815 4.52702 9.69871 4.50024 9.58776 4.50024C9.47682 4.50024 9.36738 4.52702 9.268 4.57847C9.24412 4.59078 9.22091 4.60446 9.19849 4.61947L4.00275 7.70747L4.00083 7.70847L3.99508 7.71197C3.94651 7.7411 3.90614 7.78305 3.87804 7.83361C3.84993 7.88416 3.83507 7.94154 3.83496 7.99997C3.87649 8.23763 3.93253 8.47227 4.00275 8.70247L9.51202 12.905L15.1728 8.70247Z"
        fill="#0A2767"
      />
      <path d="M11.5063 2.5H8.15051L7.18164 4L8.15051 5.5L11.5063 8.5H14.3827V5.5L11.5063 2.5Z" fill="#28A8EA" />
      <path d="M4.79395 2.5H8.14975V5.5H4.79395V2.5Z" fill="#0078D4" />
      <path d="M11.5054 2.5H14.3818V5.5H11.5054V2.5Z" fill="#50D9FF" />
      <path d="M11.5055 8.5L8.14975 5.5H4.79395V8.5L8.14975 11.5L13.3426 12.384L11.5055 8.5Z" fill="#0364B8" />
      <path d="M8.1499 5.5H11.5057V8.5H8.1499V5.5Z" fill="#0078D4" />
      <path d="M4.79395 8.5H8.14975V11.5H4.79395V8.5Z" fill="#064A8C" />
      <path d="M11.5054 8.5H14.3818V11.5H11.5054V8.5Z" fill="#0078D4" />
      <path opacity="0.5" d="M9.67993 12.6091L4.02637 8.30913L4.26367 7.87413C4.26367 7.87413 9.41483 10.9341 9.49345 10.9801C9.52562 10.9936 9.56008 11 9.59472 10.9989C9.62935 10.9979 9.66342 10.9895 9.6948 10.9741L14.9361 7.85913L15.1739 8.29363L9.67993 12.6091Z" fill="#0A2767" />
      <path d="M15.1812 8.2885L15.1744 8.2925L15.173 8.2935L9.97727 11.3805C9.87408 11.4499 9.75554 11.4905 9.633 11.4985C9.51045 11.5065 9.388 11.4815 9.27734 11.426L11.0866 13.9565L15.0436 14.855V14.857C15.1356 14.7876 15.2105 14.6963 15.2622 14.5908C15.3139 14.4853 15.3408 14.3685 15.3408 14.25V8C15.3408 8.05847 15.3261 8.11592 15.2981 8.16656C15.27 8.21721 15.2297 8.25926 15.1812 8.2885Z" fill="#1490DF" />
      <path opacity="0.05" d="M15.3408 14.25V13.881L10.5549 11.037L9.97727 11.3805C9.87408 11.4499 9.75554 11.4905 9.633 11.4985C9.51045 11.5064 9.388 11.4815 9.27734 11.426L11.0866 13.9565L15.0436 14.855V14.857C15.1356 14.7875 15.2105 14.6963 15.2622 14.5908C15.3139 14.4853 15.3408 14.3685 15.3408 14.25Z" fill="black" />
      <path opacity="0.1" d="M15.3168 14.4416L10.0708 11.3251L9.97727 11.3801C9.87412 11.4496 9.75561 11.4903 9.63306 11.4983C9.51052 11.5064 9.38804 11.4815 9.27734 11.4261L11.0866 13.9566L15.0436 14.8551V14.8571C15.1778 14.7557 15.2743 14.6091 15.3168 14.4421V14.4416Z" fill="black" />
      <path
        d="M4.00276 8.295V8.29H3.99797L3.98359 8.28C3.93776 8.25067 3.89999 8.20947 3.87395 8.1604C3.8479 8.11134 3.83448 8.05608 3.83497 8V14.25C3.83485 14.3485 3.85336 14.4461 3.88946 14.5372C3.92555 14.6282 3.97851 14.711 4.04531 14.7806C4.11211 14.8503 4.19144 14.9055 4.27874 14.9432C4.36604 14.9808 4.45961 15.0001 4.55407 15H14.6215C14.6814 14.9994 14.741 14.991 14.7989 14.975C14.8289 14.9696 14.858 14.9594 14.8852 14.945C14.8953 14.944 14.9052 14.9406 14.9139 14.935C14.9532 14.9183 14.9902 14.8964 15.0242 14.87C15.0338 14.865 15.0386 14.865 15.0434 14.855L4.00276 8.295Z"
        fill="#28A8EA"
      />
      <path opacity="0.1" d="M8.62896 12.3335V4.1665C8.62846 3.9899 8.56097 3.82068 8.44123 3.6958C8.3215 3.57092 8.15925 3.50053 7.98992 3.5H4.80814V7.228L4.00275 7.707L4.00035 7.708L3.9946 7.7115C3.94605 7.74074 3.90573 7.78279 3.87771 7.83344C3.84969 7.88408 3.83494 7.94153 3.83496 8V8.0025V8V13H7.98992C8.15925 12.9995 8.3215 12.9291 8.44123 12.8042C8.56097 12.6793 8.62846 12.5101 8.62896 12.3335Z" fill="black" />
      <path opacity="0.2" d="M8.14956 12.8335V4.6665C8.14906 4.4899 8.08157 4.32068 7.96183 4.1958C7.8421 4.07092 7.67985 4.00053 7.51052 4H4.80814V7.228L4.00275 7.707L4.00035 7.708L3.9946 7.7115C3.94605 7.74074 3.90573 7.78279 3.87771 7.83344C3.84969 7.88408 3.83494 7.94153 3.83496 8V8.0025V8V13.5H7.51052C7.67985 13.4995 7.8421 13.4291 7.96183 13.3042C8.08157 13.1793 8.14906 13.0101 8.14956 12.8335Z" fill="black" />
      <path opacity="0.2" d="M8.14956 11.8335V4.6665C8.14906 4.4899 8.08157 4.32068 7.96183 4.1958C7.8421 4.07092 7.67985 4.00053 7.51052 4H4.80814V7.228L4.00275 7.707L4.00035 7.708L3.9946 7.7115C3.94605 7.74074 3.90573 7.78279 3.87771 7.83344C3.84969 7.88408 3.83494 7.94153 3.83496 8V8.0025V8V12.5H7.51052C7.67985 12.4995 7.8421 12.4291 7.96183 12.3042C8.08157 12.1793 8.14906 12.0101 8.14956 11.8335Z" fill="black" />
      <path opacity="0.2" d="M7.67016 11.8335V4.6665C7.66966 4.4899 7.60217 4.32068 7.48243 4.1958C7.3627 4.07092 7.20045 4.00053 7.03112 4H4.80814V7.228L4.00275 7.707L4.00035 7.708L3.9946 7.7115C3.94605 7.74074 3.90573 7.78279 3.87771 7.83344C3.84969 7.88408 3.83494 7.94153 3.83496 8V8.0025V8V12.5H7.03112C7.20045 12.4995 7.3627 12.4291 7.48243 12.3042C7.60217 12.1793 7.66966 12.0101 7.67016 11.8335Z" fill="black" />
      <path d="M0.639041 4H7.03136C7.20085 4 7.36339 4.07022 7.48323 4.19521C7.60308 4.32021 7.6704 4.48973 7.6704 4.6665V11.3335C7.6704 11.5103 7.60308 11.6798 7.48323 11.8048C7.36339 11.9298 7.20085 12 7.03136 12H0.639041C0.469556 12 0.307014 11.9298 0.187171 11.8048C0.0673273 11.6798 0 11.5103 0 11.3335L0 4.6665C0 4.48973 0.0673273 4.32021 0.187171 4.19521C0.307014 4.07022 0.469556 4 0.639041 4Z" fill="#0078D4" />
      <path
        d="M1.85345 6.73382C2.02326 6.35638 2.29739 6.04052 2.64063 5.82681C3.0209 5.59978 3.45388 5.48662 3.89186 5.49981C4.29741 5.49064 4.69758 5.59788 5.04865 5.80981C5.37913 6.01512 5.64528 6.31616 5.81474 6.67632C5.99934 7.0733 6.09133 7.51019 6.0832 7.95132C6.09208 8.41208 5.99734 8.86861 5.80659 9.28432C5.63344 9.65729 5.35927 9.96915 5.01797 10.1813C4.65313 10.4 4.23777 10.5102 3.81708 10.4998C3.40262 10.5101 2.9934 10.4017 2.63392 10.1863C2.3008 9.98068 2.03151 9.67934 1.85825 9.31832C1.67238 8.92719 1.57916 8.49528 1.58643 8.05882C1.57839 7.60179 1.66968 7.14879 1.85345 6.73382ZM2.6924 8.86282C2.78286 9.10142 2.93628 9.30841 3.13489 9.45982C3.33727 9.60756 3.57971 9.68378 3.82666 9.67732C4.08974 9.68799 4.34901 9.6092 4.56542 9.45282C4.76182 9.30149 4.91133 9.0934 4.99544 8.85432C5.08975 8.58795 5.13623 8.30574 5.13255 8.02182C5.13547 7.7352 5.09171 7.45015 5.00311 7.17882C4.92494 6.93472 4.78076 6.71941 4.58795 6.55882C4.37725 6.39457 4.11873 6.31105 3.85591 6.32232C3.60358 6.31566 3.35571 6.3925 3.14783 6.54182C2.94533 6.69348 2.78837 6.9021 2.69528 7.14332C2.48936 7.69631 2.48816 8.30945 2.69192 8.86332L2.6924 8.86282Z"
        fill="white"
      />
    </svg>
  );
}

export function SalesForce(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity="0.6">
        <path
          d="M6.65824 3.22143C7.17426 2.68377 7.89266 2.3503 8.68718 2.3503C9.74338 2.3503 10.6648 2.93924 11.1556 3.81354C11.582 3.62302 12.054 3.51702 12.5507 3.51702C14.4556 3.51702 16 5.07487 16 6.99645C16 8.91826 14.4556 10.4761 12.5507 10.4761C12.3225 10.4762 12.0949 10.4536 11.8712 10.4084C11.4391 11.1792 10.6156 11.7 9.67044 11.7C9.27476 11.7 8.90052 11.6086 8.5673 11.4461C8.12923 12.4766 7.10861 13.1991 5.91909 13.1991C4.68034 13.1991 3.62462 12.4152 3.21938 11.316C3.04229 11.3536 2.85881 11.3732 2.67054 11.3732C1.19568 11.3732 0 10.1652 0 8.67487C0 7.67612 0.537209 6.80408 1.33538 6.33754C1.17106 5.95943 1.07966 5.5421 1.07966 5.10334C1.07966 3.38939 2.47111 2 4.18736 2C5.19499 2 6.09049 2.47909 6.65824 3.22143Z"
          fill="#00A1E0"
        />
        <path
          d="M2.31611 7.80795C2.30609 7.83417 2.31977 7.83964 2.32295 7.84419C2.35303 7.86607 2.38359 7.88181 2.41436 7.89935C2.57754 7.98595 2.73163 8.01124 2.89277 8.01124C3.22097 8.01124 3.42473 7.83666 3.42473 7.55563V7.55017C3.42473 7.29033 3.19477 7.19598 2.97892 7.12783L2.95089 7.11871C2.78815 7.06583 2.64776 7.02026 2.64776 6.91313V6.90745C2.64776 6.81582 2.72981 6.74834 2.85699 6.74834C2.9983 6.74834 3.16605 6.7953 3.27408 6.85501C3.27408 6.85501 3.30576 6.87553 3.31739 6.84476C3.32376 6.82834 3.37847 6.68111 3.38417 6.66516C3.39032 6.64783 3.37939 6.63506 3.36822 6.62822C3.24491 6.55324 3.07442 6.50195 2.89802 6.50195L2.86521 6.50218C2.56481 6.50218 2.35513 6.68361 2.35513 6.94366V6.94914C2.35513 7.22331 2.58646 7.3122 2.80323 7.3742L2.83809 7.38492C2.99604 7.43346 3.13211 7.47517 3.13211 7.5864V7.59188C3.13211 7.69353 3.04369 7.76919 2.901 7.76919C2.84562 7.76919 2.66898 7.76805 2.47821 7.64747C2.45518 7.63401 2.44174 7.62423 2.42397 7.61351C2.41463 7.60759 2.39115 7.59732 2.3809 7.62833L2.31611 7.80795ZM7.12089 7.80795C7.11086 7.83417 7.12454 7.83964 7.12773 7.84419C7.1578 7.86607 7.18836 7.88181 7.21911 7.89935C7.3823 7.98595 7.53641 8.01124 7.69753 8.01124C8.02573 8.01124 8.2295 7.83666 8.2295 7.55563V7.55017C8.2295 7.29033 7.99953 7.19598 7.78368 7.12783L7.75565 7.11871C7.59291 7.06583 7.4525 7.02026 7.4525 6.91313V6.90745C7.4525 6.81582 7.53455 6.74834 7.66173 6.74834C7.80304 6.74834 7.97079 6.7953 8.07884 6.85501C8.07884 6.85501 8.11052 6.87553 8.12213 6.84476C8.1285 6.82834 8.18321 6.68111 8.18891 6.66516C8.19508 6.64783 8.18413 6.63506 8.17296 6.62822C8.04966 6.55324 7.87916 6.50195 7.70276 6.50195L7.66993 6.50218C7.36954 6.50218 7.15985 6.68361 7.15985 6.94366V6.94914C7.15985 7.22331 7.39119 7.3122 7.60795 7.3742L7.64282 7.38492C7.80077 7.43346 7.93708 7.47517 7.93708 7.5864V7.59188C7.93708 7.69353 7.84841 7.76919 7.70573 7.76919C7.65036 7.76919 7.47372 7.76805 7.28295 7.64747C7.25992 7.63401 7.24626 7.62468 7.22895 7.61351C7.22301 7.60964 7.1952 7.59892 7.18562 7.62833L7.12089 7.80795ZM10.401 7.25748C10.401 7.41634 10.3714 7.54146 10.313 7.6299C10.2553 7.71742 10.168 7.76004 10.0464 7.76004C9.92443 7.76004 9.83759 7.71765 9.78083 7.6299C9.72338 7.5417 9.69423 7.41634 9.69423 7.25748C9.69423 7.09885 9.72338 6.97395 9.78083 6.88642C9.83759 6.79982 9.92443 6.75765 10.0464 6.75765C10.1681 6.75765 10.2553 6.79982 10.3132 6.88642C10.3714 6.97393 10.401 7.09884 10.401 7.25748ZM10.675 6.96301C10.648 6.87205 10.6061 6.79183 10.5503 6.72506C10.4944 6.65805 10.4238 6.60426 10.3399 6.56506C10.2562 6.52609 10.1573 6.50626 10.0464 6.50626C9.93513 6.50626 9.83621 6.52609 9.75256 6.56506C9.66868 6.60426 9.59804 6.65806 9.54196 6.72506C9.48636 6.79207 9.44441 6.87229 9.41729 6.96301C9.39063 7.05348 9.37717 7.1524 9.37717 7.25748C9.37717 7.36256 9.39063 7.4617 9.41729 7.55195C9.44441 7.64266 9.48613 7.7229 9.5422 7.7899C9.59804 7.85691 9.66892 7.91047 9.75257 7.94852C9.83643 7.98659 9.93513 8.00595 10.0464 8.00595C10.1574 8.00595 10.2561 7.98659 10.3399 7.94852C10.4236 7.91047 10.4944 7.85689 10.5503 7.7899C10.6061 7.72311 10.6481 7.64289 10.675 7.55195C10.7019 7.46147 10.7153 7.36232 10.7153 7.25748C10.7153 7.15264 10.7018 7.05348 10.675 6.96301ZM12.9247 7.71762C12.9156 7.69095 12.8898 7.70098 12.8898 7.70098C12.8499 7.71626 12.8075 7.73038 12.7624 7.73744C12.7166 7.74449 12.6662 7.74816 12.6122 7.74816C12.4796 7.74816 12.3743 7.70873 12.2988 7.63078C12.2231 7.55283 12.1807 7.4268 12.1812 7.25631C12.1817 7.1011 12.219 6.98439 12.2863 6.89552C12.3531 6.80709 12.4547 6.76172 12.5903 6.76172C12.7034 6.76172 12.7895 6.77471 12.8798 6.8032C12.8798 6.8032 12.9014 6.81254 12.9117 6.78428C12.9356 6.71772 12.9534 6.67009 12.9789 6.59693C12.9862 6.57619 12.9684 6.5673 12.962 6.56478C12.9265 6.55088 12.8426 6.52833 12.7792 6.51875C12.72 6.50964 12.6507 6.50485 12.5737 6.50485C12.4585 6.50485 12.356 6.52446 12.2683 6.56366C12.1807 6.60263 12.1064 6.65641 12.0476 6.72344C11.9888 6.79045 11.9442 6.87066 11.9143 6.96137C11.8847 7.05185 11.8696 7.15123 11.8696 7.25631C11.8696 7.48354 11.9309 7.66723 12.052 7.80172C12.1732 7.93665 12.3553 8.00525 12.5928 8.00525C12.7332 8.00525 12.8773 7.97677 12.9808 7.93596C12.9808 7.93596 13.0006 7.92639 12.9919 7.90339L12.9247 7.71762ZM13.404 7.10529C13.417 7.01709 13.4414 6.94369 13.479 6.8865C13.5358 6.79966 13.6224 6.75203 13.7441 6.75203C13.8658 6.75203 13.9463 6.79988 14.0039 6.8865C14.0422 6.94369 14.0588 7.0203 14.0655 7.10529H13.404ZM14.3264 6.91132C14.3032 6.82357 14.2455 6.73491 14.2077 6.69434C14.1479 6.63007 14.0896 6.58517 14.0317 6.56009C13.956 6.52772 13.8653 6.50631 13.766 6.50631C13.6502 6.50631 13.5451 6.52569 13.4599 6.56579C13.3744 6.60591 13.3026 6.66062 13.2463 6.72877C13.19 6.79668 13.1476 6.87759 13.1207 6.96945C13.0936 7.06085 13.0799 7.16045 13.0799 7.2655C13.0799 7.37241 13.0941 7.47201 13.1221 7.56157C13.1504 7.65184 13.1955 7.73137 13.2566 7.79723C13.3174 7.86357 13.3958 7.91554 13.4897 7.95175C13.5829 7.98777 13.6962 8.00647 13.8264 8.00623C14.0942 8.00531 14.2352 7.94561 14.2934 7.91346C14.3036 7.90778 14.3134 7.89775 14.3011 7.86903L14.2405 7.69921C14.2314 7.67393 14.2056 7.68326 14.2056 7.68326C14.1393 7.70788 14.0449 7.7521 13.825 7.75165C13.6812 7.75143 13.5745 7.70903 13.5077 7.64269C13.4391 7.57479 13.4056 7.47494 13.3997 7.3341L14.3271 7.33501C14.3271 7.33501 14.3515 7.33456 14.354 7.31084C14.3549 7.30085 14.3859 7.12033 14.3264 6.91132ZM5.97683 7.10529C5.99004 7.01709 6.01421 6.94369 6.05182 6.8865C6.10856 6.79966 6.19518 6.75203 6.31688 6.75203C6.43859 6.75203 6.51905 6.79988 6.57693 6.8865C6.61499 6.94369 6.63163 7.0203 6.63824 7.10529H5.97683ZM6.89898 6.91132C6.87575 6.82357 6.81831 6.73491 6.78048 6.69434C6.72076 6.63007 6.66241 6.58517 6.60453 6.56009C6.52886 6.52772 6.43816 6.50631 6.33878 6.50631C6.22322 6.50631 6.11792 6.52569 6.03268 6.56579C5.94722 6.60591 5.87542 6.66062 5.81912 6.72877C5.76283 6.79668 5.72043 6.87759 5.69354 6.96945C5.66665 7.06085 5.65275 7.16045 5.65275 7.2655C5.65275 7.37241 5.66687 7.47201 5.69492 7.56157C5.72319 7.65184 5.76832 7.73137 5.82939 7.79723C5.89025 7.86357 5.96865 7.91554 6.06255 7.95175C6.15578 7.98777 6.26904 8.00647 6.39919 8.00623C6.66699 8.00531 6.80808 7.94561 6.8662 7.91346C6.87647 7.90778 6.88625 7.89775 6.87395 7.86903L6.81356 7.69921C6.8042 7.67393 6.77846 7.68326 6.77846 7.68326C6.71214 7.70788 6.618 7.7521 6.3976 7.75165C6.25402 7.75143 6.14735 7.70903 6.08057 7.64269C6.01195 7.57479 5.97846 7.47494 5.97252 7.3341L6.89993 7.33501C6.89993 7.33501 6.92431 7.33456 6.92682 7.31084C6.9277 7.30085 6.95869 7.12033 6.89898 6.91132ZM3.97225 7.71254C3.93601 7.68358 3.93099 7.6763 3.91869 7.6576C3.90045 7.62911 3.89111 7.58854 3.89111 7.53704C3.89111 7.45543 3.918 7.39686 3.97385 7.35743C3.97318 7.35767 4.05363 7.28792 4.24279 7.29042C4.37566 7.29225 4.4944 7.31185 4.4944 7.31185V7.73351C4.4944 7.73351 4.3768 7.75879 4.24415 7.76677C4.05544 7.77817 3.97156 7.71232 3.97225 7.71254ZM4.34124 7.06091C4.30364 7.05817 4.25485 7.05657 4.19651 7.05657C4.11697 7.05657 4.04016 7.0666 3.96813 7.08598C3.89566 7.10534 3.83048 7.13568 3.7744 7.17576C3.71842 7.21572 3.6724 7.26806 3.63993 7.3287C3.60712 7.38979 3.59048 7.46181 3.59048 7.5425C3.59048 7.62455 3.6046 7.69588 3.63287 7.75425C3.66114 7.81282 3.70193 7.86159 3.75389 7.89921C3.8054 7.93681 3.869 7.96438 3.94283 7.98102C4.01554 7.99766 4.09804 8.00608 4.18831 8.00608C4.28335 8.00608 4.37816 7.99835 4.47001 7.9826C4.56095 7.9671 4.67263 7.94453 4.70362 7.93748C4.72535 7.93225 4.74701 7.9267 4.76858 7.92084C4.79161 7.91516 4.78979 7.89053 4.78979 7.89053L4.78932 7.04245C4.78932 6.85645 4.73964 6.71855 4.64186 6.63309C4.54453 6.54786 4.40118 6.50476 4.21589 6.50476C4.14636 6.50476 4.03446 6.51435 3.96745 6.52779C3.96745 6.52779 3.76483 6.56699 3.6814 6.63218C3.6814 6.63218 3.66317 6.64358 3.6732 6.66911L3.73885 6.84553C3.74705 6.86832 3.76917 6.86057 3.76917 6.86057C3.76917 6.86057 3.77623 6.85783 3.78445 6.85304C3.96291 6.75595 4.18855 6.75891 4.18855 6.75891C4.28884 6.75891 4.36588 6.77897 4.41783 6.81885C4.46843 6.85759 4.49418 6.91616 4.49418 7.03969V7.0789C4.4144 7.06751 4.34124 7.06091 4.34124 7.06091ZM11.8212 6.58306C11.8282 6.56208 11.8134 6.55205 11.8073 6.54978C11.7916 6.54363 11.7127 6.52699 11.6518 6.52311C11.5354 6.51605 11.4707 6.53565 11.4127 6.56163C11.3553 6.58761 11.2915 6.62954 11.2559 6.67718V6.56437C11.2559 6.54864 11.2448 6.5361 11.2293 6.5361H10.9915C10.9761 6.5361 10.9649 6.54862 10.9649 6.56437V7.94761C10.9649 7.96311 10.9777 7.97588 10.9931 7.97588H11.2368C11.2443 7.97583 11.2514 7.97283 11.2567 7.96753C11.2619 7.96224 11.2648 7.95507 11.2648 7.94761V7.25656C11.2648 7.1638 11.2751 7.07127 11.2956 7.01315C11.3157 6.9557 11.343 6.90967 11.3767 6.87662C11.4107 6.8438 11.4492 6.82078 11.4914 6.8078C11.5345 6.79457 11.5821 6.79024 11.6158 6.79024C11.6644 6.79024 11.7177 6.80277 11.7177 6.80277C11.7355 6.80482 11.7455 6.79388 11.7514 6.7777C11.7674 6.73531 11.8125 6.60835 11.8212 6.58306Z"
          fill="white"
        />
        <path
          d="M9.53581 5.94161C9.50618 5.9325 9.47929 5.92633 9.44418 5.91973C9.40861 5.91336 9.36623 5.91016 9.31815 5.91016C9.15038 5.91016 9.01819 5.95756 8.92544 6.051C8.83312 6.14401 8.77043 6.28554 8.73898 6.47176L8.72758 6.53443H8.517C8.517 6.53443 8.49147 6.53352 8.48599 6.56134L8.45159 6.75438C8.44907 6.77262 8.45707 6.78423 8.48167 6.78423H8.68657L8.4787 7.9448C8.46251 8.03825 8.44382 8.11507 8.42308 8.1734C8.40279 8.23084 8.38297 8.27391 8.35836 8.30537C8.33465 8.33546 8.31231 8.35779 8.27355 8.37078C8.24165 8.38149 8.20473 8.3865 8.16439 8.3865C8.14205 8.3865 8.11221 8.38284 8.09009 8.37829C8.06821 8.37395 8.05658 8.36917 8.03994 8.3621C8.03994 8.3621 8.01602 8.353 8.00644 8.37693C7.99891 8.39675 7.94422 8.54694 7.9376 8.56542C7.93123 8.58387 7.94034 8.59824 7.95195 8.60256C7.97931 8.61214 7.99958 8.61851 8.03674 8.6274C8.08826 8.63949 8.13178 8.64017 8.17257 8.64017C8.25781 8.64017 8.33576 8.62809 8.40027 8.60485C8.46501 8.58137 8.52151 8.54056 8.57166 8.48542C8.62568 8.4257 8.65964 8.36325 8.69202 8.27779C8.72415 8.19345 8.75174 8.08861 8.77361 7.96645L8.98261 6.78423H9.28802C9.28802 6.78423 9.31376 6.78514 9.31903 6.75712L9.35366 6.5643C9.35595 6.54585 9.3482 6.53444 9.32334 6.53444H9.02682C9.02842 6.52785 9.04186 6.42344 9.07583 6.32523C9.09042 6.28352 9.11776 6.24955 9.14079 6.2263C9.1636 6.20351 9.1898 6.18733 9.21853 6.17798C9.24792 6.1684 9.28144 6.16385 9.31813 6.16385C9.34594 6.16385 9.3735 6.16704 9.39424 6.17136C9.42296 6.17753 9.43412 6.18071 9.44167 6.183C9.47198 6.19212 9.47607 6.18323 9.48201 6.16865L9.5529 5.97401C9.56018 5.953 9.54218 5.94411 9.53581 5.94161ZM5.39278 7.94731C5.39278 7.96282 5.38162 7.97536 5.36612 7.97536H5.12019C5.10469 7.97536 5.09375 7.96282 5.09375 7.94731V5.96805C5.09375 5.95257 5.10469 5.94003 5.12019 5.94003H5.36612C5.38162 5.94003 5.39278 5.95257 5.39278 5.96805V7.94731Z"
          fill="white"
        />
      </g>
    </svg>
  );
}

export function SAP(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M0 11.8261H7.99548L15.8202 4H0V11.8261Z" fill="url(#paint0_linear_201_66)" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.38836 5.56525H7.82592L7.83114 9.24004L6.47062 5.56404H5.12157L3.96018 8.63412C3.8367 7.85291 3.02905 7.58334 2.39357 7.38143C1.97392 7.24665 1.52853 7.04838 1.53305 6.82925C1.53653 6.64943 1.77131 6.48265 2.23792 6.50751C2.55096 6.52438 2.82749 6.5496 3.37757 6.81534L3.91844 5.87273C3.41688 5.61743 2.72331 5.45621 2.15462 5.45569H2.15114C1.48801 5.45569 0.935834 5.67047 0.593573 6.02438C0.354964 6.27134 0.226268 6.58543 0.221051 6.93273C0.212355 7.41065 0.387486 7.74943 0.755486 8.02021C1.06644 8.24804 1.46418 8.39586 1.81462 8.50438C2.24679 8.6383 2.59983 8.75482 2.59549 9.00282C2.59258 9.09378 2.55602 9.18042 2.49288 9.24595C2.38505 9.35725 2.21983 9.39899 1.99114 9.40352C1.54992 9.41291 1.22296 9.34351 0.701921 9.03551L0.220703 9.9903C0.741225 10.2863 1.29375 10.4348 1.92157 10.4348L2.06279 10.4338C2.60923 10.4239 3.0527 10.2929 3.40505 10.0094C3.42522 9.99325 3.44331 9.97691 3.46209 9.96038L3.40296 10.2651L4.72122 10.2609L4.95775 9.65534C5.20644 9.74021 5.48923 9.78717 5.7894 9.78717C6.08192 9.78717 6.35705 9.74265 6.60105 9.66265L6.76592 10.2609L9.13114 10.2632L9.13688 8.88265H9.64018C10.8567 8.88265 11.5758 8.26351 11.5758 7.22525C11.5755 6.06891 10.8764 5.56525 9.38836 5.56525ZM5.7894 8.70647C5.61924 8.70763 5.45027 8.67805 5.29062 8.61917L5.78383 7.06178H5.7934L6.27862 8.62351C6.13253 8.67569 5.96609 8.70647 5.78923 8.70647H5.7894ZM9.47983 7.81169H9.13653V6.55656H9.48001C9.9374 6.55656 10.3026 6.70891 10.3026 7.17604C10.3023 7.65951 9.9374 7.81169 9.48001 7.81169"
        fill="white"
      />
      <defs>
        <linearGradient id="paint0_linear_201_66" x1="7.91009" y1="4" x2="7.91009" y2="11.8263" gradientUnits="userSpaceOnUse">
          <stop stopColor="#00AEEF" />
          <stop offset="0.212" stopColor="#0097DC" />
          <stop offset="0.519" stopColor="#007CC5" />
          <stop offset="0.792" stopColor="#006CB8" />
          <stop offset="1" stopColor="#0066B3" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export function SuccessFactors(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_201_5)">
        <path
          d="M5.45646 2.63964C3.71171 2.63964 2.53453 3.94294 2.53453 5.87687C2.53453 7.97897 4.44745 9.99699 6.65466 12.3513L7.97898 13.7597L9.3033 12.3513C11.5105 10.018 13.4234 7.99999 13.4234 5.87687C13.4234 3.94294 12.2462 2.63964 10.5015 2.63964C9.76576 2.63964 9.05104 2.95495 8.52552 3.54354L7.95796 4.15315L7.39039 3.54354C6.90691 2.95495 6.21321 2.63964 5.45646 2.63964ZM7.99999 16.0721L5.58258 13.5075C3.12312 10.9219 1 8.67267 1 5.87687C1 3.06006 2.89189 1 5.47747 1C6.4024 1 7.26426 1.31531 7.99999 1.88288C8.73573 1.31531 9.61861 1 10.5225 1C13.1081 1 15 3.06006 15 5.87687C15 8.67267 12.8769 10.9219 10.4174 13.5075L7.99999 16.0721Z"
          fill="#F0AB00"
        />
      </g>
      <defs>
        <clipPath id="clip0_201_5">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function CherryworkSmall(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.8537 0.860517C10.0442 1.00957 9.69098 1.04424 9.44857 0.892355C9.20616 0.740465 7.94812 -0.0775881 6.56135 0.395723C5.17458 0.869034 4.91524 1.67127 4.58548 1.81201C4.25571 1.95274 3.94496 2.61302 3.69942 2.8945C3.45387 3.17597 4.14098 2.8367 4.46072 2.96101C4.78045 3.08532 5.52065 2.89064 6.26189 2.713C7.00313 2.53536 8.92989 1.65687 9.32486 1.37966C9.71982 1.10244 10.5125 1.0335 10.6402 0.970226C10.7678 0.906956 10.8537 0.860517 10.8537 0.860517Z" fill="#649B45" />
      <g style={{ mixBlendMode: "multiply" }}>
        <g style={{ mixBlendMode: "multiply" }}>
          <path d="M3.80035 2.94327C3.79659 2.94874 3.79282 2.95442 3.78906 2.95969C3.83587 2.91914 3.88665 2.87412 3.94141 2.82788C3.89069 2.86803 3.84367 2.9065 3.80035 2.94327Z" fill="#D1D1D1" />
        </g>
        <g style={{ mixBlendMode: "multiply" }}>
          <path
            d="M9.70129 1.1134H9.70401C9.57193 1.099 9.46932 1.07528 9.41144 1.03999L9.39681 1.03107C8.74752 1.03208 7.73545 0.607848 8.08026 0.753857C8.47898 0.922983 8.98554 1.04263 8.81606 1.0986C8.64658 1.15457 8.14337 1.15781 7.3967 0.912033C7.10526 0.814077 6.82073 0.697767 6.54491 0.563843L6.5215 0.571346C6.54783 0.591625 6.58984 0.622246 6.65023 0.666252C7.0592 0.964555 8.2232 1.21561 8.02404 1.25759C7.82489 1.29956 6.45985 1.0479 6.76391 1.15416C7.06798 1.26043 7.55343 1.30139 7.29388 1.33039C7.03433 1.35939 6.00658 1.39346 5.63899 1.49262C5.2714 1.59178 5.09189 1.62666 5.2714 1.59178C5.45091 1.5569 6.20762 1.41921 6.49245 1.49262C6.77729 1.56603 5.58111 1.73495 4.82169 2.19062C4.51155 2.38014 4.21737 2.59319 3.94189 2.82779C4.42934 2.4467 4.9813 2.15069 5.57317 1.95295C6.96161 1.48654 9.37988 1.09698 9.70129 1.1134Z"
            fill="#D1D1D1"
          />
        </g>
      </g>
      <path d="M7.60359 4.14785C5.63044 3.66744 0.43737 3.5119 1.04988 9.34778C1.74786 16.0013 10.4168 18.0475 13.6416 13.7268C16.8663 9.40618 15.7313 4.29609 12.1871 3.87408C9.40079 3.54232 9.78364 4.67916 7.60359 4.14785Z" fill="#D70100" />
      <g style={{ mixBlendMode: "multiply" }}>
        <path d="M15.5098 9.10626C15.7656 6.38887 14.4827 4.14704 12.1871 3.87428C9.40084 3.54191 9.78368 4.67895 7.60364 4.14764C6.62145 3.90815 4.83762 3.74977 3.37207 4.37355C4.69322 4.12554 5.68105 4.77264 6.63001 5.24271C8.11835 5.97985 10.4518 4.66618 11.811 5.11678C13.1701 5.56737 14.7598 6.99116 15.5098 9.10626Z" fill="#F5F5F5" />
      </g>
      <g style={{ mixBlendMode: "screen" }}>
        <path d="M12.187 3.87407C9.40074 3.54231 9.78359 4.67935 7.60354 4.14804C6.57057 3.89638 4.65489 3.73557 3.15234 4.47636C4.89082 3.95397 6.18543 3.99473 7.58599 4.37719C9.40074 4.87281 10.2063 3.35534 12.69 4.29162C13.4743 4.58749 14.2009 5.08027 14.8287 5.61604C14.2536 4.65968 13.3514 4.01278 12.187 3.87407Z" fill="#4A4A4A" />
      </g>
      <g style={{ mixBlendMode: "screen" }}>
        <path d="M1.05007 9.34573C1.24212 11.1761 2.03936 12.6553 3.15843 13.7515C2.97808 13.3172 2.80693 12.8795 2.65083 12.445C1.06093 8.00874 2.96952 5.67017 2.96952 5.67017C2.96952 5.67017 2.09182 5.99341 1.16563 6.98546C1.00388 7.6352 0.952057 8.4133 1.05007 9.34573Z" fill="#242424" />
      </g>
      <g style={{ mixBlendMode: "multiply" }}>
        <path d="M8.03001 4.50269C7.24865 4.83911 7.76503 5.40469 8.56625 5.35663C9.36746 5.30857 10.0794 4.94112 10.7277 4.83506C11.3759 4.729 11.6704 4.83262 11.6704 4.83262C11.6704 4.83262 10.6717 4.68783 9.30331 5.11653C7.38386 5.71598 8.03001 4.50269 8.03001 4.50269Z" fill="#E8E8E8" />
      </g>
      <g style={{ mixBlendMode: "multiply" }}>
        <path d="M13.6445 13.7259C15.0112 11.8953 15.5924 9.92357 15.5414 8.22561C14.6783 11.1296 12.2813 14.1838 9.08253 13.6168C5.04364 12.9007 2.58671 10.2647 5.28104 3.95142C3.09724 4.08688 0.618982 5.21358 1.05282 9.34684C1.7508 16.0004 10.4198 18.0453 13.6445 13.7259Z" fill="#F5F5F5" />
      </g>
      <g style={{ mixBlendMode: "multiply" }}>
        <path d="M1.16992 10.0986C2.41584 16.1479 10.5415 17.8787 13.6412 13.7268C14.5737 12.478 15.1394 11.1635 15.3901 9.91431C12.8597 14.1684 7.11572 16.0369 1.16992 10.0986Z" fill="#F5F5F5" />
      </g>
      <path
        d="M8.37677 11.739C7.79164 12.2636 6.85333 12.3699 6.16622 11.9882C5.75432 11.7595 5.56123 11.3705 5.5489 10.9418C5.51776 9.87942 5.53761 8.79226 5.53615 7.72843C5.53615 7.71484 5.53615 7.68361 5.53615 7.63879H6.39421C6.39421 7.86855 6.39275 8.12711 6.39421 8.34247C6.39776 9.11307 6.39797 9.88489 6.40779 10.6567C6.41343 11.1028 6.65856 11.3535 7.09031 11.377C7.57827 11.4036 7.88338 11.1852 7.91054 10.7271C7.94377 10.1593 7.93144 9.58943 7.93499 9.0204C7.93771 8.63855 7.93917 7.87038 7.93708 7.64772C8.10698 7.65076 8.48168 7.64447 8.80998 7.64914C8.80998 7.74526 8.80329 7.84929 8.8035 7.92817C8.80684 8.84925 8.80245 9.77052 8.81666 10.6914C8.82356 11.1318 9.10464 11.3655 9.56752 11.3559C10.0605 11.3462 10.3131 11.1215 10.3169 10.6648C10.3244 9.76626 10.3219 8.86729 10.3234 7.96772C10.3234 7.87951 10.3313 7.75317 10.3313 7.64995H11.2028C11.2028 7.67023 11.2055 7.72741 11.2055 7.74708C11.2055 8.75819 11.2264 9.77295 11.1885 10.7826C11.1526 11.74 10.5976 12.2097 9.60534 12.2109C8.99053 12.2121 8.69922 12.1014 8.37677 11.739Z"
        fill="white"
        stroke="white"
        strokeWidth="0.309547"
        strokeMiterlimit="10"
      />
      <path
        d="M11.7711 0C11.9767 0.170343 12.2137 0.371916 12.4189 0.542259C12.4659 0.581195 12.5121 0.620739 12.56 0.660891L12.2845 0.843402C12.2074 0.802844 12.1621 0.742007 11.8687 0.582615C11.2466 0.952097 10.6514 1.19544 9.8673 2.33147C9.65687 2.59875 9.35468 3.12377 8.71981 4.28211C8.66757 4.37742 8.56956 4.56216 8.60216 4.78908C8.62975 4.98031 8.92148 5.16647 8.81469 5.20155C8.53633 5.29301 8.23227 5.32607 7.97544 5.17965C7.80262 4.98863 7.8908 4.77407 7.99258 4.60353C8.19445 4.16145 8.50331 3.79237 8.74949 3.37482C9.06107 2.84656 9.37349 2.38744 9.6857 1.89264C9.6857 1.89264 10.2543 0.973796 11.4982 0.16791C11.5479 0.135666 11.5976 0.104639 11.5976 0.104639L11.7711 0Z"
        fill="#468D41"
      />
      <g style={{ mixBlendMode: "screen" }}>
        <path
          d="M6.52441 5.15039L6.60968 5.19825C6.66589 5.22806 6.74614 5.27815 6.85209 5.33047C6.96013 5.37772 7.08573 5.44464 7.24225 5.49128C7.32264 5.52171 7.40537 5.54599 7.48968 5.56388C7.58102 5.58966 7.67391 5.60998 7.76782 5.62472C7.86879 5.64399 7.97102 5.65646 8.07377 5.66203C8.18317 5.67183 8.29307 5.67536 8.4029 5.67257C8.64088 5.66766 8.87824 5.64733 9.11342 5.61174C9.60389 5.53995 10.1219 5.41179 10.6469 5.29255C10.7804 5.26578 10.9062 5.23414 11.0471 5.21447C11.1135 5.20514 11.1781 5.19419 11.2456 5.18608L11.4546 5.17391C11.7232 5.16825 11.9912 5.20098 12.25 5.27105C12.4919 5.34116 12.7237 5.44099 12.9396 5.56814C13.1427 5.69529 13.3287 5.82568 13.4829 5.96865C13.7983 6.24525 14.0054 6.53159 14.145 6.73053C14.2168 6.82908 14.263 6.91304 14.2975 6.96941L14.3491 7.05519L14.2881 6.97631C14.2482 6.925 14.1907 6.84916 14.1124 6.7573C13.9034 6.5036 13.6675 6.27207 13.4083 6.06659C13.2528 5.93681 13.0671 5.82325 12.8679 5.71212C12.6568 5.60586 12.4344 5.52216 12.2046 5.46248C11.9618 5.40599 11.7123 5.38164 11.4627 5.39009L11.2747 5.40226C11.2103 5.40996 11.1436 5.42253 11.078 5.43227C10.9545 5.45011 10.8199 5.48499 10.691 5.51156C10.1685 5.62492 9.64339 5.75207 9.1362 5.80682C8.89257 5.83662 8.64672 5.84585 8.40144 5.8344C8.28738 5.83076 8.17364 5.8206 8.06081 5.80398C7.95462 5.79203 7.8494 5.77306 7.74588 5.7472C7.64936 5.72758 7.5545 5.70094 7.46209 5.6675C7.37719 5.64086 7.29448 5.60805 7.21466 5.56935C7.06044 5.50669 6.93505 5.42558 6.83056 5.36778C6.72775 5.30694 6.65607 5.24428 6.60299 5.20839L6.52441 5.15039Z"
          fill="#6B6B6B"
        />
      </g>
      <g style={{ mixBlendMode: "screen" }}>
        <path d="M6.43359 5.53381C7.78024 6.34842 9.33022 6.29144 10.8181 5.89235C12.306 5.49326 13.6287 6.22877 14.3686 7.3575C14.3686 7.3575 12.5403 6.33078 11.1364 6.6739C8.51876 7.3137 6.43359 5.53381 6.43359 5.53381Z" fill="#242424" />
      </g>
      <g style={{ mixBlendMode: "screen" }}>
        <path d="M11.1354 6.6739C12.5393 6.33078 14.3677 7.3575 14.3677 7.3575C13.798 6.48855 12.8827 5.85523 11.8129 5.79034C12.2234 6.02112 12.7176 6.32246 12.5962 6.37438C12.3951 6.46036 9.62014 6.79902 8.9092 6.50376C8.52113 6.34254 8.83271 6.30948 9.36038 6.15354C8.34622 6.23587 7.34313 6.08459 6.43262 5.53381C6.43345 5.53381 8.51862 7.3137 11.1354 6.6739Z" fill="#242424" />
      </g>
    </svg>
  );
}

export function SystemDefault(props) {
  return (
    <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.6668 11.0007H8.66683V8.33398H7.3335V11.0007H5.3335V11.6673H1.3335V13.0007H5.3335V13.6673H10.6668V13.0007H14.6668V11.6673H10.6668V11.0007Z" fill="#6B6B6B" />
      <path d="M12 2.66667H8.138L7.56933 2.09733L7.47133 2H4C3.73489 2.00035 3.48074 2.10582 3.29328 2.29328C3.10582 2.48074 3.00035 2.73489 3 3V8.33333C3.00035 8.59844 3.10582 8.85259 3.29328 9.04005C3.48074 9.22751 3.73489 9.33298 4 9.33333H12C12.2651 9.33298 12.5193 9.22751 12.7067 9.04005C12.8942 8.85259 12.9996 8.59844 13 8.33333V3.66667C12.9996 3.40156 12.8942 3.14741 12.7067 2.95995C12.5193 2.77249 12.2651 2.66702 12 2.66667V2.66667ZM11.6667 4V8H4.33333V3.33333H6.862L7.43067 3.90267L7.52867 4H11.6667Z" fill="#6B6B6B" />
    </svg>
  );
}

export function CreatedDate(props) {
  return (
    <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M8.48299 13.2568L7.12018 11.894C7.02786 11.8017 6.98278 11.6856 6.98493 11.5459C6.98706 11.4062 7.03427 11.2901 7.12657 11.1978C7.21889 11.1055 7.33491 11.0594 7.47464 11.0594C7.61439 11.0594 7.73041 11.1055 7.82272 11.1978L8.88811 12.2696L11.3702 9.78757C11.4668 9.69099 11.5821 9.6427 11.7163 9.6427C11.8505 9.6427 11.9693 9.69441 12.0727 9.79782C12.1693 9.90124 12.2176 10.0194 12.2176 10.1523C12.2176 10.2852 12.1693 10.4012 12.0727 10.5004L9.32658 13.2568C9.2129 13.3704 9.0723 13.4273 8.90478 13.4273C8.73725 13.4273 8.59666 13.3704 8.48299 13.2568ZM3.99966 9.02857L3.27786 9.75037C3.18555 9.84268 3.07059 9.88777 2.93299 9.88564C2.79539 9.88349 2.68044 9.83627 2.58812 9.74397C2.49582 9.65166 2.44967 9.53563 2.44967 9.3959C2.44967 9.25616 2.49582 9.14013 2.58812 9.04783L3.30352 8.33243L2.58172 7.61063C2.48941 7.51832 2.44432 7.4023 2.44646 7.26257C2.4486 7.12282 2.49582 7.0068 2.58812 6.9145C2.68044 6.82219 2.79646 6.77603 2.93619 6.77603C3.07594 6.77603 3.19196 6.82219 3.28426 6.9145L3.99966 7.6363L4.72146 6.9145C4.81377 6.82219 4.92979 6.77603 5.06952 6.77603C5.20927 6.77603 5.32529 6.82219 5.41759 6.9145C5.5099 7.0068 5.55606 7.12282 5.55606 7.26257C5.55606 7.4023 5.5099 7.51832 5.41759 7.61063L4.69579 8.33243L5.41759 9.05423C5.5099 9.14655 5.55606 9.2615 5.55606 9.3991C5.55606 9.5367 5.5099 9.65166 5.41759 9.74397C5.32529 9.83627 5.20927 9.88242 5.06952 9.88242C4.92979 9.88242 4.81377 9.83627 4.72146 9.74397L3.99966 9.02857ZM1.53814 13.3324C1.20139 13.3324 0.916341 13.2158 0.683008 12.9824C0.449675 12.7491 0.333008 12.464 0.333008 12.1273V3.20425C0.333008 2.86749 0.449675 2.58245 0.683008 2.34912C0.916341 2.11578 1.20139 1.99912 1.53814 1.99912H2.46122V1.10167C2.46122 0.955523 2.51016 0.833517 2.60802 0.73565C2.70588 0.637795 2.82789 0.588867 2.97404 0.588867C3.12019 0.588867 3.24219 0.637795 3.34006 0.73565C3.43791 0.833517 3.48684 0.955523 3.48684 1.10167V1.99912H8.53811V1.08885C8.53811 0.946973 8.58597 0.828173 8.68169 0.732451C8.77741 0.636728 8.89621 0.588867 9.03809 0.588867C9.17997 0.588867 9.29877 0.636728 9.39449 0.732451C9.49022 0.828173 9.53809 0.946973 9.53809 1.08885V1.99912H10.4612C10.7979 1.99912 11.083 2.11578 11.3163 2.34912C11.5496 2.58245 11.6663 2.86749 11.6663 3.20425V7.22602L10.6663 8.24267V5.87092H1.33299V12.1273C1.33299 12.1871 1.35222 12.2363 1.39069 12.2747C1.42916 12.3132 1.47831 12.3324 1.53814 12.3324H5.31374L6.31629 13.3324H1.53814ZM1.33299 4.87093H10.6663V3.20425C10.6663 3.14442 10.6471 3.09527 10.6086 3.0568C10.5702 3.01833 10.521 2.9991 10.4612 2.9991H1.53814C1.47831 2.9991 1.42916 3.01833 1.39069 3.0568C1.35222 3.09527 1.33299 3.14442 1.33299 3.20425V4.87093Z"
        fill="#002D5D"
      />
    </svg>
  );
}

export function DueDate(props) {
  return (
    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M7.79454 11.002C7.36634 11.002 7.00288 10.8527 6.70416 10.554C6.40545 10.2552 6.25609 9.89178 6.25609 9.46358C6.25609 9.03538 6.40545 8.67192 6.70416 8.3732C7.00288 8.07449 7.36634 7.92513 7.79454 7.92513C8.22274 7.92513 8.5862 8.07449 8.88492 8.3732C9.18364 8.67192 9.33299 9.03538 9.33299 9.46358C9.33299 9.89178 9.18364 10.2552 8.88492 10.554C8.5862 10.8527 8.22274 11.002 7.79454 11.002ZM1.53814 13.3353C1.20139 13.3353 0.916341 13.2187 0.683008 12.9853C0.449674 12.752 0.333008 12.467 0.333008 12.1302V3.20718C0.333008 2.87042 0.449674 2.58538 0.683008 2.35205C0.916341 2.11871 1.20139 2.00205 1.53814 2.00205H2.46122V1.1046C2.46122 0.958453 2.51016 0.836447 2.60802 0.73858C2.70588 0.640725 2.82789 0.591797 2.97404 0.591797C3.12019 0.591797 3.24219 0.640725 3.34006 0.73858C3.43791 0.836447 3.48684 0.958453 3.48684 1.1046V2.00205H8.53811V1.09178C8.53811 0.949902 8.58597 0.831103 8.68169 0.73538C8.77741 0.639658 8.89621 0.591797 9.03809 0.591797C9.17997 0.591797 9.29877 0.639658 9.39449 0.73538C9.49022 0.831103 9.53809 0.949902 9.53809 1.09178V2.00205H10.4612C10.7979 2.00205 11.083 2.11871 11.3163 2.35205C11.5496 2.58538 11.6663 2.87042 11.6663 3.20718V12.1302C11.6663 12.467 11.5496 12.752 11.3163 12.9853C11.083 13.2187 10.7979 13.3353 10.4612 13.3353H1.53814ZM1.53814 12.3354H10.4612C10.521 12.3354 10.5702 12.3161 10.6086 12.2777C10.6471 12.2392 10.6663 12.19 10.6663 12.1302V5.87385H1.33299V12.1302C1.33299 12.19 1.35222 12.2392 1.39069 12.2777C1.42916 12.3161 1.47831 12.3354 1.53814 12.3354ZM1.33299 4.87386H10.6663V3.20718C10.6663 3.14735 10.6471 3.0982 10.6086 3.05973C10.5702 3.02126 10.521 3.00203 10.4612 3.00203H1.53814C1.47831 3.00203 1.42916 3.02126 1.39069 3.05973C1.35222 3.0982 1.33299 3.14735 1.33299 3.20718V4.87386Z"
        fill="#CD4968"
      />
    </svg>
  );
}

export function Workflow(props) {
  return (
    <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        style={{ fill: "inherit" }}
        d="M7.00027 0.593966C7.66688 0.593966 8.23373 0.827532 8.70082 1.29467C9.16791 1.7618 9.40145 2.32865 9.40145 2.99523C9.40145 3.58707 9.2147 4.10468 8.8412 4.54808C8.46769 4.99148 7.99846 5.26308 7.4335 5.36288L7.4335 6.7696L9.79503 6.7696C10.0898 6.7696 10.3421 6.87454 10.552 7.08443C10.7619 7.29432 10.8668 7.54664 10.8668 7.84138L10.8668 8.63497L12.0989 8.63497C12.2507 8.63497 12.378 8.68632 12.4807 8.78903C12.5834 8.89174 12.6348 9.01902 12.6348 9.17087L12.6348 12.4991C12.6348 12.6537 12.5834 12.7821 12.4807 12.8842C12.378 12.9864 12.2507 13.0375 12.0989 13.0375L8.77067 13.0375C8.61607 13.0375 8.48768 12.9861 8.3855 12.8834C8.28332 12.7807 8.23223 12.6534 8.23223 12.5016L8.23223 9.1734C8.23223 9.0188 8.28359 8.89041 8.3863 8.78823C8.48902 8.68605 8.6163 8.63497 8.76813 8.63497L10.0002 8.63497L10.0002 7.84138C10.0002 7.78155 9.98095 7.7324 9.94248 7.69393C9.90402 7.65547 9.85487 7.63623 9.79503 7.63623L4.00533 7.63623C3.9455 7.63623 3.89635 7.65547 3.85788 7.69393C3.81941 7.7324 3.80018 7.78155 3.80018 7.84138L3.80018 8.676C4.36514 8.77087 4.83437 9.04 5.20787 9.4834C5.58138 9.9268 5.76813 10.4444 5.76813 11.0362C5.76813 11.7028 5.53455 12.2697 5.0674 12.7368C4.60025 13.2039 4.03338 13.4375 3.36677 13.4375C2.70016 13.4375 2.13332 13.2039 1.66623 12.7368C1.19914 12.2697 0.965598 11.7028 0.965598 11.0362C0.965598 10.4444 1.15235 9.9268 1.52585 9.4834C1.89935 9.04 2.36858 8.77087 2.93353 8.676L2.93353 7.84138C2.93353 7.54664 3.03848 7.29432 3.24838 7.08443C3.45827 6.87454 3.71059 6.7696 4.00533 6.7696L6.56687 6.7696L6.56687 5.35548C6.00191 5.26061 5.53268 4.99148 5.15917 4.54808C4.78567 4.10468 4.59892 3.58707 4.59892 2.99523C4.59892 2.32865 4.83249 1.7618 5.29965 1.29467C5.76679 0.827532 6.33367 0.593966 7.00027 0.593966ZM3.36315 9.50162C2.93827 9.50162 2.5769 9.65179 2.27903 9.95213C1.98117 10.2525 1.83223 10.6151 1.83223 11.0399C1.83223 11.4648 1.9824 11.8262 2.28275 12.1241C2.58309 12.4219 2.9457 12.5709 3.37058 12.5709C3.79546 12.5709 4.15683 12.4207 4.45468 12.1203C4.75255 11.82 4.90148 11.4574 4.90148 11.0325C4.90148 10.6076 4.75131 10.2463 4.45097 9.94842C4.15062 9.65055 3.78802 9.50162 3.36315 9.50162ZM11.7681 9.50162L9.09888 9.50162L9.09888 12.1709L11.7681 12.1709L11.7681 9.50162ZM6.99647 1.46061C6.57159 1.46061 6.21022 1.61079 5.91235 1.91113C5.61448 2.21147 5.46555 2.57407 5.46555 2.99895C5.46555 3.42383 5.61572 3.7852 5.91607 4.08307C6.21641 4.38093 6.57902 4.52987 7.0039 4.52987C7.42878 4.52987 7.79015 4.37969 8.08802 4.07935C8.38588 3.779 8.53482 3.41639 8.53482 2.99152C8.53482 2.56665 8.38464 2.20528 8.0843 1.90742C7.78396 1.60955 7.42134 1.46061 6.99647 1.46061Z"
      />
    </svg>
  );
}

export function BookMarkIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="15" viewBox="0 0 12 15" fill="none">
      <path
        d="M5.99998 12.7304L2.70575 13.9864C2.18653 14.1826 1.68911 14.1281 1.21348 13.8227C0.737825 13.5174 0.5 13.0987 0.5 12.5668V1.62111C0.5 1.19566 0.655417 0.836035 0.96625 0.542246C1.27708 0.248458 1.65757 0.101562 2.1077 0.101562H9.89225C10.3424 0.101562 10.7229 0.248458 11.0337 0.542246C11.3445 0.836035 11.5 1.19566 11.5 1.62111V12.5668C11.5 13.0987 11.2621 13.5174 10.7865 13.8227C10.3108 14.1281 9.81342 14.1826 9.2942 13.9864L5.99998 12.7304ZM5.99998 11.3963L9.76725 12.8067C9.86982 12.8491 9.96758 12.8401 10.0605 12.7795C10.1535 12.7189 10.2 12.6371 10.2 12.5341V1.62111C10.2 1.54839 10.1679 1.48173 10.1038 1.42113C10.0397 1.36055 9.96918 1.33025 9.89225 1.33025H2.1077C2.03077 1.33025 1.96024 1.36055 1.89613 1.42113C1.83203 1.48173 1.79997 1.54839 1.79997 1.62111V12.5341C1.79997 12.6371 1.84645 12.7189 1.9394 12.7795C2.03237 12.8401 2.13013 12.8491 2.2327 12.8067L5.99998 11.3963Z"
        fill="#1D1D11"
      />
    </svg>
  );
}

export function BookMarkFilledIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="14" viewBox="0 0 11 14" fill="none">
      <path d="M5.5 12.3494L2.14141 13.8565C1.63156 14.0845 1.1472 14.0408 0.688327 13.7255C0.229442 13.4103 0 12.9669 0 12.3953V1.59898C0 1.15217 0.148078 0.773969 0.444232 0.464381C0.740387 0.154794 1.10218 0 1.5296 0H9.4704C9.89782 0 10.2596 0.154794 10.5558 0.464381C10.8519 0.773969 11 1.15217 11 1.59898V12.3953C11 12.9669 10.7706 13.4103 10.3117 13.7255C9.8528 14.0408 9.36844 14.0845 8.85859 13.8565L5.5 12.3494Z" fill="#BDBDBD" />
    </svg>
  );
}

export function BookmarkAddIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
      <path
        d="M6.99997 14.4615L3.03075 16.1653C2.4282 16.423 1.85577 16.3744 1.31347 16.0196C0.771158 15.6648 0.5 15.1627 0.5 14.5134V2.3077C0.5 1.80257 0.675 1.375 1.025 1.025C1.375 0.675 1.80257 0.5 2.3077 0.5H7.99997V1.99998H2.3077C2.23077 1.99998 2.16024 2.03203 2.09613 2.09613C2.03202 2.16024 1.99997 2.23077 1.99997 2.3077V14.4788C1.99997 14.5878 2.04645 14.6743 2.1394 14.7384C2.23237 14.8025 2.33013 14.8121 2.4327 14.7673L6.99997 12.8L11.5672 14.7673C11.6698 14.8121 11.7676 14.8025 11.8605 14.7384C11.9535 14.6743 12 14.5878 12 14.4788V7.99998H13.5V14.5134C13.5 15.1627 13.2288 15.6648 12.6865 16.0196C12.1442 16.3744 11.5717 16.423 10.9692 16.1653L6.99997 14.4615ZM12 3.99998H10.75C10.5375 3.99998 10.3594 3.92807 10.2156 3.78425C10.0718 3.64045 9.99998 3.46225 9.99998 3.24965C9.99998 3.03707 10.0718 2.85898 10.2156 2.7154C10.3594 2.5718 10.5375 2.5 10.75 2.5H12V1.25C12 1.0375 12.0719 0.859375 12.2157 0.715625C12.3595 0.571875 12.5377 0.5 12.7503 0.5C12.9629 0.5 13.141 0.571875 13.2845 0.715625C13.4282 0.859375 13.5 1.0375 13.5 1.25V2.5H14.75C14.9625 2.5 15.1406 2.57191 15.2843 2.71573C15.4281 2.85954 15.5 3.03774 15.5 3.25032C15.5 3.46292 15.4281 3.64102 15.2843 3.7846C15.1406 3.92818 14.9625 3.99998 14.75 3.99998H13.5V5.25C13.5 5.46248 13.428 5.6406 13.2842 5.78435C13.1404 5.9281 12.9622 5.99998 12.7496 5.99998C12.537 5.99998 12.3589 5.9281 12.2154 5.78435C12.0718 5.6406 12 5.46248 12 5.25V3.99998Z"
        fill="#1D1D11"
      />
    </svg>
  );
}

export function ArrowOutward() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="11" viewBox="0 0 12 11" fill="none">
      <path
        d="M10.1203 2.06825L1.84305 10.3375C1.72767 10.4529 1.60347 10.5058 1.47047 10.4962C1.33746 10.4865 1.21326 10.424 1.09789 10.3087C0.982499 10.1933 0.924805 10.0669 0.924805 9.92967C0.924805 9.79239 0.982499 9.66606 1.09789 9.55067L9.3463 1.29423H2.16197C2.0085 1.29423 1.87985 1.24272 1.77603 1.13969C1.67223 1.03666 1.62033 0.908994 1.62033 0.756688C1.62033 0.604383 1.67223 0.475348 1.77603 0.369584C1.87985 0.263821 2.0085 0.210938 2.16197 0.210938H10.5338C10.7236 0.210938 10.8826 0.275132 11.011 0.403521C11.1394 0.53191 11.2036 0.691 11.2036 0.880792V9.25258C11.2036 9.40606 11.1521 9.53469 11.0491 9.6385C10.946 9.74232 10.8184 9.79423 10.6661 9.79423C10.5138 9.79423 10.3847 9.74232 10.279 9.6385C10.1732 9.53469 10.1203 9.40606 10.1203 9.25258V2.06825Z"
        fill="black"
      />
    </svg>
  );
}

export function MarkEmailUnread() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="15" viewBox="0 0 17 15" fill="none">
      <path
        d="M2.02598 14.25C1.59957 14.25 1.23864 14.1073 0.943183 13.822C0.647728 13.5366 0.5 13.188 0.5 12.7762V3.49459C0.5 3.08277 0.647728 2.73418 0.943183 2.44883C1.23864 2.16348 1.59957 2.02081 2.02598 2.02081H10.487C10.447 2.22463 10.4256 2.42714 10.4229 2.62833C10.4202 2.82955 10.4362 3.03467 10.4708 3.24371H1.76622V12.7762C1.76622 12.8494 1.79057 12.9095 1.83928 12.9565C1.88799 13.0036 1.95022 13.0271 2.02598 13.0271H15.013C15.0887 13.0271 15.151 13.0036 15.1997 12.9565C15.2484 12.9095 15.2727 12.8494 15.2727 12.7762V6.52522C15.5152 6.47296 15.7405 6.40398 15.9489 6.31828C16.1572 6.23257 16.3539 6.12753 16.539 6.00316V12.7762C16.539 13.188 16.3912 13.5366 16.0958 13.822C15.8003 14.1073 15.4394 14.25 15.013 14.25H2.02598ZM8.51948 7.32012L11.5682 5.44183C11.7165 5.57667 11.8742 5.69817 12.0414 5.80635C12.2086 5.91453 12.3836 6.01095 12.5665 6.09561L8.92208 8.34077C8.79221 8.41707 8.65801 8.45652 8.51948 8.45913C8.38095 8.46175 8.24675 8.42491 8.11688 8.3486L1.76622 4.50581V3.24371L8.51948 7.32012ZM14.4286 4.98403C13.7846 4.98403 13.2368 4.76584 12.7849 4.32946C12.3331 3.89308 12.1072 3.36394 12.1072 2.74203C12.1072 2.12012 12.3331 1.59097 12.7849 1.15459C13.2368 0.718197 13.7846 0.5 14.4286 0.5C15.0725 0.5 15.6204 0.718197 16.0722 1.15459C16.5241 1.59097 16.75 2.12012 16.75 2.74203C16.75 3.36394 16.5241 3.89308 16.0722 4.32946C15.6204 4.76584 15.0725 4.98403 14.4286 4.98403Z"
        fill="#002D5D"
      />
    </svg>
  );
}

export function ContactMail() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="15" viewBox="0 0 19 15" fill="none">
      <path
        d="M11.8207 6.62702H15.8968C16.1068 6.62702 16.2822 6.55567 16.4227 6.41297C16.5633 6.27028 16.6336 6.0923 16.6336 5.87906V3.39641C16.6336 3.18315 16.5633 3.00517 16.4227 2.86247C16.2822 2.71977 16.1068 2.64842 15.8968 2.64842H11.8207C11.6106 2.64842 11.4353 2.71977 11.2947 2.86247C11.1541 3.00517 11.0838 3.18315 11.0838 3.39641V5.87906C11.0838 6.0923 11.1541 6.27028 11.2947 6.41297C11.4353 6.55567 11.6106 6.62702 11.8207 6.62702ZM13.8587 4.86053L15.3825 3.77677C15.486 3.69931 15.595 3.69161 15.7094 3.75368C15.8239 3.81575 15.8811 3.91044 15.8811 4.03776C15.8811 4.06216 15.8362 4.15075 15.7463 4.30353L14.2804 5.34115C14.1467 5.43557 14.0061 5.48279 13.8587 5.48279C13.7114 5.48279 13.5708 5.43557 13.437 5.34115L11.9712 4.30353C11.9524 4.28442 11.9074 4.19583 11.8363 4.03776C11.8363 3.91044 11.8936 3.81575 12.008 3.75368C12.1225 3.69161 12.2314 3.69931 12.3349 3.77677L13.8587 4.86053ZM1.47367 14.25C1.06188 14.25 0.713318 14.1052 0.427991 13.8155C0.142664 13.5259 0 13.1721 0 12.7541V1.99594C0 1.57793 0.142664 1.22411 0.427991 0.934464C0.713318 0.644821 1.06188 0.5 1.47367 0.5H17.2763C17.6881 0.5 18.0367 0.644821 18.322 0.934464C18.6073 1.22411 18.75 1.57793 18.75 1.99594V12.7541C18.75 13.1721 18.6073 13.5259 18.322 13.8155C18.0367 14.1052 17.6881 14.25 17.2763 14.25H1.47367ZM12.0997 13.0087H17.2763C17.3391 13.0087 17.3966 12.9822 17.4488 12.9291C17.5011 12.8761 17.5272 12.8177 17.5272 12.7541V1.99594C17.5272 1.93229 17.5011 1.87393 17.4488 1.82087C17.3966 1.76783 17.3391 1.74131 17.2763 1.74131H1.47367C1.41096 1.74131 1.35346 1.76783 1.30119 1.82087C1.24894 1.87393 1.22281 1.93229 1.22281 1.99594V12.7541C1.22281 12.8177 1.24894 12.8761 1.30119 12.9291C1.35346 12.9822 1.41096 13.0087 1.47367 13.0087H1.759C2.32965 12.1175 3.07067 11.412 3.98204 10.8921C4.89341 10.3722 5.87584 10.1123 6.92935 10.1123C7.98286 10.1123 8.96529 10.3722 9.87666 10.8921C10.788 11.412 11.529 12.1175 12.0997 13.0087ZM6.92935 9.1256C7.55121 9.1256 8.08032 8.90413 8.51666 8.46118C8.95301 8.01822 9.17118 7.48111 9.17118 6.84984C9.17118 6.21858 8.95301 5.68147 8.51666 5.23851C8.08032 4.79557 7.55121 4.57409 6.92935 4.57409C6.30749 4.57409 5.77838 4.79557 5.34203 5.23851C4.90569 5.68147 4.68751 6.21858 4.68751 6.84984C4.68751 7.48111 4.90569 8.01822 5.34203 8.46118C5.77838 8.90413 6.30749 9.1256 6.92935 9.1256ZM3.30162 13.0087H10.5571C10.0899 12.4846 9.54171 12.0777 8.91254 11.7881C8.28335 11.4984 7.62229 11.3536 6.92935 11.3536C6.23641 11.3536 5.57744 11.4984 4.95244 11.7881C4.32744 12.0777 3.77717 12.4846 3.30162 13.0087ZM6.92935 7.8843C6.64612 7.8843 6.40546 7.78377 6.20739 7.58272C6.00933 7.38166 5.9103 7.13737 5.9103 6.84984C5.9103 6.56232 6.00933 6.31803 6.20739 6.11698C6.40546 5.91593 6.64612 5.8154 6.92935 5.8154C7.21258 5.8154 7.45324 5.91593 7.65131 6.11698C7.84937 6.31803 7.94839 6.56232 7.94839 6.84984C7.94839 7.13737 7.84937 7.38166 7.65131 7.58272C7.45324 7.78377 7.21258 7.8843 6.92935 7.8843Z"
        fill="#002D5D"
      />
    </svg>
  );
}

export function AccountBalance() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M2.62558 12.9605V7.12712C2.62558 6.97365 2.67709 6.84501 2.78012 6.74119C2.88315 6.63738 3.01082 6.58548 3.16312 6.58548C3.31543 6.58548 3.44447 6.63738 3.55025 6.74119C3.65601 6.84501 3.70889 6.97365 3.70889 7.12712V12.9605C3.70889 13.114 3.65738 13.2426 3.55435 13.3464C3.45131 13.4503 3.32364 13.5022 3.17133 13.5022C3.01904 13.5022 2.89 13.4503 2.78423 13.3464C2.67846 13.2426 2.62558 13.114 2.62558 12.9605ZM7.45889 12.9605V7.12712C7.45889 6.97365 7.51041 6.84501 7.61344 6.74119C7.71646 6.63738 7.84413 6.58548 7.99644 6.58548C8.14874 6.58548 8.27778 6.63738 8.38354 6.74119C8.4893 6.84501 8.54218 6.97365 8.54218 7.12712V12.9605C8.54218 13.114 8.49067 13.2426 8.38764 13.3464C8.28462 13.4503 8.15695 13.5022 8.00464 13.5022C7.85234 13.5022 7.7233 13.4503 7.61754 13.3464C7.51178 13.2426 7.45889 13.114 7.45889 12.9605ZM1.18327 15.5855C1.0298 15.5855 0.90115 15.534 0.797331 15.4309C0.693511 15.3279 0.641602 15.2002 0.641602 15.0479C0.641602 14.8956 0.693511 14.7666 0.797331 14.6608C0.90115 14.555 1.0298 14.5022 1.18327 14.5022H14.8178C14.9713 14.5022 15.0999 14.5537 15.2037 14.6567C15.3076 14.7597 15.3595 14.8874 15.3595 15.0397C15.3595 15.192 15.3076 15.321 15.2037 15.4268C15.0999 15.5326 14.9713 15.5855 14.8178 15.5855H1.18327ZM12.2922 12.9605V7.12712C12.2922 6.97365 12.3437 6.84501 12.4467 6.74119C12.5498 6.63738 12.6774 6.58548 12.8297 6.58548C12.982 6.58548 13.1111 6.63738 13.2169 6.74119C13.3226 6.84501 13.3755 6.97365 13.3755 7.12712V12.9605C13.3755 13.114 13.324 13.2426 13.221 13.3464C13.1179 13.4503 12.9903 13.5022 12.838 13.5022C12.6856 13.5022 12.5566 13.4503 12.4508 13.3464C12.3451 13.2426 12.2922 13.114 12.2922 12.9605ZM14.5999 5.58548H1.32108C1.13234 5.58548 0.971914 5.51987 0.839789 5.38865C0.707664 5.25742 0.641602 5.09809 0.641602 4.91065V4.49823C0.641602 4.37585 0.675726 4.25456 0.743976 4.13433C0.812213 4.01413 0.904692 3.92947 1.02141 3.88037L7.42202 0.752187C7.60382 0.65177 7.79632 0.601562 7.99952 0.601562C8.20271 0.601562 8.39589 0.65177 8.57906 0.752187L14.946 3.85954C15.0646 3.92685 15.1632 4.01899 15.2417 4.13598C15.3202 4.25296 15.3595 4.38429 15.3595 4.52996V4.82792C15.3595 5.04578 15.2877 5.2265 15.1441 5.37008C15.0005 5.51368 14.8191 5.58548 14.5999 5.58548ZM2.24252 4.50217H13.7586L8.11273 1.73775C8.07534 1.72172 8.03794 1.71371 8.00054 1.71371C7.96314 1.71371 7.92574 1.72172 7.88835 1.73775L2.24252 4.50217Z"
        fill="#002D5D"
      />
    </svg>
  );
}

export function Campaign() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12" fill="none">
      <path
        d="M15.1075 6.54054H13.515C13.3618 6.54054 13.2323 6.48912 13.1266 6.38629C13.0208 6.28344 12.9679 6.15578 12.9679 6.00329C12.9679 5.85079 13.0196 5.72166 13.1229 5.6159C13.2262 5.51013 13.3545 5.45725 13.5077 5.45725H15.1002C15.2535 5.45725 15.3829 5.50867 15.4887 5.6115C15.5945 5.71435 15.6474 5.84201 15.6474 5.9945C15.6474 6.147 15.5957 6.27613 15.4924 6.3819C15.389 6.48766 15.2608 6.54054 15.1075 6.54054ZM11.8894 10.0934C11.9823 9.97485 12.1015 9.90088 12.2467 9.8715C12.392 9.84211 12.524 9.86694 12.6426 9.946L13.9455 10.8469C14.0769 10.9322 14.1573 11.0544 14.1866 11.2136C14.216 11.3728 14.1842 11.5117 14.0913 11.6303C13.9983 11.7489 13.8792 11.8229 13.7339 11.8522C13.5886 11.8816 13.4567 11.8568 13.3381 11.7777L12.0352 10.8768C11.9038 10.7915 11.8234 10.6693 11.794 10.5101C11.7647 10.3509 11.7964 10.212 11.8894 10.0934ZM13.9118 1.08863L12.5897 2.01973C12.4711 2.09878 12.3357 2.12015 12.1834 2.08383C12.0312 2.0475 11.9156 1.97004 11.8365 1.85146C11.7574 1.73287 11.7326 1.59747 11.762 1.44523C11.7914 1.29299 11.8654 1.17734 11.9839 1.09827L13.3061 0.188021C13.4246 0.108965 13.5601 0.0841244 13.7123 0.113499C13.8645 0.142874 13.9802 0.216862 14.0592 0.335459C14.1383 0.454042 14.1631 0.589451 14.1337 0.741687C14.1044 0.893923 14.0304 1.00957 13.9118 1.08863ZM2.20831 7.64631H1.69227C1.32252 7.64631 1.00679 7.51547 0.745081 7.25377C0.483386 6.99206 0.352539 6.67633 0.352539 6.30658V5.69121C0.352539 5.32146 0.483386 5.00573 0.745081 4.74402C1.00679 4.48233 1.32252 4.35148 1.69227 4.35148H4.47591L7.65058 2.48931C7.87387 2.35256 8.09582 2.34429 8.31643 2.46448C8.53706 2.58467 8.64737 2.77458 8.64737 3.03419V8.94277C8.64737 9.20238 8.53359 9.39229 8.30602 9.51248C8.07846 9.63267 7.85304 9.6244 7.62975 9.48765L4.60091 7.64631H3.7916V10.7672C3.7916 10.9911 3.7158 11.1802 3.56421 11.3346C3.41261 11.489 3.22452 11.5662 2.99996 11.5662C2.77539 11.5662 2.5873 11.4904 2.43571 11.3388C2.28411 11.1872 2.20831 10.9991 2.20831 10.7745V7.64631ZM7.56406 8.17358V3.78254L4.78039 5.43479H1.69227C1.62817 5.43479 1.56941 5.4615 1.51598 5.51492C1.46256 5.56835 1.43585 5.62711 1.43585 5.69121V6.30658C1.43585 6.37068 1.46256 6.42944 1.51598 6.48287C1.56941 6.53629 1.62817 6.563 1.69227 6.563H4.92623L7.56406 8.17358ZM9.96789 8.24408V3.75371C10.2393 4.02188 10.4471 4.35654 10.5913 4.75771C10.7355 5.15889 10.8076 5.57262 10.8076 5.9989C10.8076 6.42517 10.7355 6.8389 10.5913 7.24008C10.4471 7.64125 10.2393 7.97592 9.96789 8.24408Z"
        fill="#002D5D"
      />
    </svg>
  );
}

export const SaveAsDraftIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M15.5 3.83333V13.8333C15.5 14.2917 15.3369 14.6842 15.0108 15.0108C14.6842 15.3369 14.2917 15.5 13.8333 15.5H2.16667C1.70833 15.5 1.31583 15.3369 0.989167 15.0108C0.663055 14.6842 0.5 14.2917 0.5 13.8333V2.16667C0.5 1.70833 0.663055 1.31583 0.989167 0.989167C1.31583 0.663055 1.70833 0.5 2.16667 0.5H12.1667L15.5 3.83333ZM13.8333 4.54167L11.4583 2.16667H2.16667V13.8333H13.8333V4.54167ZM8 13C8.69444 13 9.28472 12.7569 9.77083 12.2708C10.2569 11.7847 10.5 11.1944 10.5 10.5C10.5 9.80556 10.2569 9.21528 9.77083 8.72917C9.28472 8.24306 8.69444 8 8 8C7.30556 8 6.71528 8.24306 6.22917 8.72917C5.74306 9.21528 5.5 9.80556 5.5 10.5C5.5 11.1944 5.74306 11.7847 6.22917 12.2708C6.71528 12.7569 7.30556 13 8 13ZM3 6.33333H10.5V3H3V6.33333Z"
        fill="#323232"
      />
    </svg>
  );
};

export const ViewMoreIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path d="M15.75 2.25H11.25M15.75 2.25L9 9M15.75 2.25V6.75" stroke="#0070F2" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M15.75 9.75V14.25C15.75 14.6478 15.592 15.0294 15.3107 15.3107C15.0294 15.592 14.6478 15.75 14.25 15.75H3.75C3.35218 15.75 2.97064 15.592 2.68934 15.3107C2.40804 15.0294 2.25 14.6478 2.25 14.25V3.75C2.25 3.35218 2.40804 2.97064 2.68934 2.68934C2.97064 2.40804 3.35218 2.25 3.75 2.25H8.25" stroke="#0070F2" stroke-width="1.5" stroke-linecap="round" />
    </svg>
  );
};

export const ArrowDownConnectorIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="45" viewBox="0 0 8 45" fill="none">
      <path d="M4.5 1C4.5 0.723858 4.27614 0.5 4 0.5C3.72386 0.5 3.5 0.723858 3.5 1H4.5ZM3.64645 44.6559C3.84171 44.8511 4.15829 44.8511 4.35355 44.6559L7.53553 41.4739C7.7308 41.2786 7.7308 40.9621 7.53553 40.7668C7.34027 40.5715 7.02369 40.5715 6.82843 40.7668L4 43.5952L1.17157 40.7668C0.976311 40.5715 0.659728 40.5715 0.464466 40.7668C0.269204 40.9621 0.269204 41.2786 0.464466 41.4739L3.64645 44.6559ZM3.5 1V44.3023H4.5V1H3.5Z" fill="#BDBDBD" />
    </svg>
  );
};

export const ContactPhoneNumber = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
      <path
        d="M12.4707 13.5599C10.9557 13.4349 9.49955 13.035 8.10212 12.3603C6.70469 11.6857 5.43948 10.7805 4.30648 9.64479C3.17346 8.50911 2.27656 7.24737 1.61577 5.85956C0.954979 4.47174 0.555139 3.02036 0.41625 1.50542C0.388472 1.21146 0.473791 0.956077 0.672208 0.739272C0.870625 0.522466 1.11864 0.414062 1.41625 0.414062H3.5925C3.84464 0.414062 4.06152 0.489653 4.24314 0.640834C4.42477 0.792014 4.55137 0.989397 4.62294 1.23298L5.05883 2.97654C5.09196 3.16245 5.08154 3.34407 5.02758 3.52142C4.97364 3.69876 4.88576 3.84299 4.76396 3.9541L2.83929 5.8275C3.14912 6.4087 3.49261 6.9557 3.86975 7.46852C4.24689 7.98135 4.66013 8.46095 5.10948 8.90731C5.5533 9.35091 6.03269 9.76181 6.54765 10.14C7.0626 10.5182 7.61869 10.8622 8.21592 11.1721L10.1903 9.24419C10.3067 9.1224 10.4403 9.0412 10.5909 9.0006C10.7416 8.96001 10.9098 8.9536 11.0957 8.98138L12.764 9.34835C13.0161 9.4178 13.2156 9.546 13.3625 9.73296C13.5094 9.91993 13.5829 10.1422 13.5829 10.3996V12.5599C13.5829 12.8575 13.471 13.1055 13.2472 13.3039C13.0235 13.5023 12.7646 13.5876 12.4707 13.5599ZM2.35212 4.76983L3.92265 3.26342C3.94935 3.24204 3.96672 3.21266 3.97473 3.17527C3.98274 3.13787 3.98141 3.10315 3.97073 3.0711L3.60521 1.62558C3.59453 1.58285 3.57583 1.5508 3.54912 1.52944C3.5224 1.50806 3.48767 1.49738 3.44494 1.49738H1.6454C1.61335 1.49738 1.58665 1.50806 1.56527 1.52944C1.5439 1.5508 1.53321 1.57751 1.53321 1.60956C1.57594 2.13734 1.66916 2.66378 1.81285 3.1889C1.95656 3.71401 2.13632 4.24099 2.35212 4.76983ZM9.31044 11.6801C9.80863 11.8993 10.3295 12.0657 10.8732 12.1794C11.4168 12.2931 11.9216 12.3766 12.3874 12.4301C12.4194 12.4301 12.4461 12.4194 12.4675 12.398C12.4889 12.3766 12.4996 12.3499 12.4996 12.3179V10.523C12.4996 10.4803 12.4889 10.4456 12.4675 10.4188C12.4461 10.3921 12.4141 10.3734 12.3714 10.3628L11.0172 10.0695C10.9851 10.0588 10.9571 10.0575 10.9331 10.0655C10.909 10.0735 10.8836 10.0909 10.8569 10.1176L9.31044 11.6801Z"
        fill="#002D5D"
      />
    </svg>
  );
};

export const DeleteWarningIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <rect width="24" height="24" rx="12" fill="#FFEEEE" />
      <path
        d="M19.6508 16.2251L13.5019 5.54677C13.3483 5.28515 13.1289 5.06823 12.8656 4.91751C12.6023 4.76679 12.3042 4.6875 12.0008 4.6875C11.6974 4.6875 11.3992 4.76679 11.1359 4.91751C10.8726 5.06823 10.6532 5.28515 10.4996 5.54677L4.35075 16.2251C4.20291 16.4782 4.125 16.766 4.125 17.059C4.125 17.3521 4.20291 17.6399 4.35075 17.8929C4.50244 18.1561 4.72142 18.3742 4.98523 18.5249C5.24903 18.6755 5.54816 18.7532 5.85193 18.75H18.1496C18.4531 18.753 18.7519 18.6751 19.0155 18.5245C19.279 18.3739 19.4978 18.1559 19.6493 17.8929C19.7974 17.64 19.8756 17.3523 19.8758 17.0592C19.8761 16.7662 19.7984 16.4783 19.6508 16.2251ZM11.4383 10.3125C11.4383 10.1634 11.4975 10.0203 11.603 9.9148C11.7085 9.80931 11.8516 9.75005 12.0008 9.75005C12.1499 9.75005 12.293 9.80931 12.3985 9.9148C12.504 10.0203 12.5633 10.1634 12.5633 10.3125V13.125C12.5633 13.2742 12.504 13.4173 12.3985 13.5228C12.293 13.6283 12.1499 13.6875 12.0008 13.6875C11.8516 13.6875 11.7085 13.6283 11.603 13.5228C11.4975 13.4173 11.4383 13.2742 11.4383 13.125V10.3125ZM12.0008 16.5C11.8339 16.5 11.6707 16.4506 11.532 16.3578C11.3932 16.2651 11.2851 16.1334 11.2212 15.9792C11.1574 15.825 11.1407 15.6554 11.1732 15.4917C11.2058 15.328 11.2861 15.1777 11.4041 15.0597C11.5221 14.9417 11.6725 14.8613 11.8361 14.8288C11.9998 14.7962 12.1695 14.8129 12.3236 14.8768C12.4778 14.9406 12.6096 15.0488 12.7023 15.1875C12.795 15.3263 12.8445 15.4894 12.8445 15.6563C12.8445 15.8801 12.7556 16.0947 12.5974 16.2529C12.4391 16.4112 12.2245 16.5 12.0008 16.5Z"
        fill="#EF8533"
      />
    </svg>
  );
};

export const SendBack = (props) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <g clip-path="url(#clip0_20862_19130)">
        <path d="M3 4.5L6 7.5L6 5.25H11.25C12.075 5.25 12.75 5.925 12.75 6.75C12.75 7.575 12.075 8.25 11.25 8.25H6C4.3425 8.25 3 9.5925 3 11.25C3 12.9075 4.3425 14.25 6 14.25H11.25V16.5L14.25 13.5L11.25 10.5V12.75H6C5.175 12.75 4.5 12.075 4.5 11.25C4.5 10.425 5.175 9.75 6 9.75H11.25C12.9075 9.75 14.25 8.4075 14.25 6.75C14.25 5.0925 12.9075 3.75 11.25 3.75H6V1.5L3 4.5Z" fill="#1D1D11" />
      </g>
      <defs>
        <clipPath id="clip0_20862_19130">
          <rect width="18" height="18" fill="white" transform="matrix(0 -1 1 0 0 18)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const SendBackWhite = (props) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path d="M3 5L6 8V5.75H11.25C12.075 5.75 12.75 6.425 12.75 7.25C12.75 8.075 12.075 8.75 11.25 8.75H6C4.3425 8.75 3 10.0925 3 11.75C3 13.4075 4.3425 14.75 6 14.75H11.25V17L14.25 14L11.25 11V13.25H6C5.175 13.25 4.5 12.575 4.5 11.75C4.5 10.925 5.175 10.25 6 10.25H11.25C12.9075 10.25 14.25 8.9075 14.25 7.25C14.25 5.5925 12.9075 4.25 11.25 4.25H6V2L3 5Z" fill="white" />
    </svg>
  );
};

export function KpiIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
      <path
        d="M1.75674 5.83848C1.39135 5.83848 1.07671 5.70897 0.812826 5.44994C0.548937 5.19092 0.416992 4.87386 0.416992 4.49875V2.0116C0.416992 1.64623 0.548937 1.3316 0.812826 1.06771C1.07671 0.803819 1.39135 0.671875 1.75674 0.671875H12.2439C12.619 0.671875 12.936 0.803819 13.1951 1.06771C13.4541 1.3316 13.5836 1.64623 13.5836 2.0116V4.49875C13.5836 4.87386 13.4541 5.19092 13.1951 5.44994C12.936 5.70897 12.619 5.83848 12.2439 5.83848H1.75674ZM1.75674 4.75519H12.2439C12.308 4.75519 12.3667 4.72847 12.4202 4.67504C12.4736 4.62162 12.5003 4.56286 12.5003 4.49875V2.0116C12.5003 1.94749 12.4736 1.88873 12.4202 1.83531C12.3667 1.7819 12.308 1.75519 12.2439 1.75519H1.75674C1.69263 1.75519 1.63386 1.7819 1.58043 1.83531C1.52701 1.88873 1.5003 1.94749 1.5003 2.0116V4.49875C1.5003 4.56286 1.52701 4.62162 1.58043 4.67504C1.63386 4.72847 1.69263 4.75519 1.75674 4.75519ZM1.75674 13.3256C1.39135 13.3256 1.07671 13.1961 0.812826 12.9371C0.548937 12.6781 0.416992 12.361 0.416992 11.9859V9.49875C0.416992 9.13338 0.548937 8.81874 0.812826 8.55485C1.07671 8.29097 1.39135 8.15902 1.75674 8.15902H12.2439C12.619 8.15902 12.936 8.29097 13.1951 8.55485C13.4541 8.81874 13.5836 9.13338 13.5836 9.49875V11.9859C13.5836 12.361 13.4541 12.6781 13.1951 12.9371C12.936 13.1961 12.619 13.3256 12.2439 13.3256H1.75674ZM1.75674 12.2423H12.2439C12.308 12.2423 12.3667 12.2156 12.4202 12.1622C12.4736 12.1088 12.5003 12.05 12.5003 11.9859V9.49875C12.5003 9.43464 12.4736 9.37587 12.4202 9.32246C12.3667 9.26903 12.308 9.24231 12.2439 9.24231H1.75674C1.69263 9.24231 1.63386 9.26903 1.58043 9.32246C1.52701 9.37587 1.5003 9.43464 1.5003 9.49875V11.9859C1.5003 12.05 1.52701 12.1088 1.58043 12.1622C1.63386 12.2156 1.69263 12.2423 1.75674 12.2423Z"
        fill="#1C1B1F"
      />
    </svg>
  );
}

export function FavouriteIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <g mask="url(#mask0_13104_56154)">
        <path
          d="M9.99228 15.9429C9.82776 15.9429 9.66937 15.9143 9.51714 15.8571C9.36489 15.8 9.22039 15.7099 9.08364 15.5871L8.09489 14.6865C6.57565 13.3392 5.24953 12.0366 4.11651 10.7786C2.9835 9.52059 2.41699 8.19074 2.41699 6.78904C2.41699 5.68945 2.79389 4.76872 3.54768 4.02685C4.30147 3.28499 5.23707 2.91406 6.35447 2.91406C7.00144 2.91406 7.64155 3.06204 8.27478 3.35798C8.90803 3.65392 9.48321 4.15713 10.0003 4.8676C10.5452 4.15713 11.1291 3.65392 11.752 3.35798C12.375 3.06204 13.0041 2.91406 13.6395 2.91406C14.7572 2.91406 15.6939 3.28499 16.4498 4.02685C17.2057 4.76872 17.5836 5.68945 17.5836 6.78904C17.5836 8.20676 17.0038 9.54864 15.844 10.8147C14.6843 12.0807 13.3715 13.3686 11.9057 14.6784L10.909 15.5871C10.7722 15.7099 10.6264 15.8 10.4715 15.8571C10.3165 15.9143 10.1568 15.9429 9.99228 15.9429ZM9.48428 6.02944C9.10288 5.34247 8.64215 4.83205 8.10208 4.49819C7.56201 4.16431 6.97947 3.99738 6.35447 3.99738C5.539 3.99738 4.85944 4.26126 4.31578 4.78904C3.77213 5.31682 3.5003 5.98622 3.5003 6.79723C3.5003 7.44669 3.7089 8.1189 4.1261 8.81388C4.54329 9.50885 5.06705 10.2065 5.69739 10.9068C6.32773 11.6071 7.0123 12.2874 7.75108 12.9477C8.48985 13.6079 9.1835 14.2265 9.83201 14.8035C9.8801 14.8462 9.93619 14.8676 10.0003 14.8676C10.0644 14.8676 10.1205 14.8462 10.1686 14.8035C10.8171 14.2265 11.5108 13.6079 12.2495 12.9477C12.9883 12.2874 13.6729 11.6071 14.3032 10.9068C14.9336 10.2065 15.4573 9.50885 15.8745 8.81388C16.2917 8.1189 16.5003 7.44669 16.5003 6.79723C16.5003 5.98622 16.2285 5.31682 15.6848 4.78904C15.1412 4.26126 14.4616 3.99738 13.6461 3.99738C13.0211 3.99738 12.4351 4.16431 11.8881 4.49819C11.3411 4.83205 10.8769 5.34247 10.4955 6.02944C10.4528 6.13627 10.3846 6.21293 10.291 6.25942C10.1974 6.30589 10.1001 6.32913 9.9993 6.32913C9.89849 6.32913 9.79812 6.30589 9.69822 6.25942C9.59833 6.21293 9.52702 6.13627 9.48428 6.02944Z"
          fill="#1C1B1F"
        />
      </g>
    </svg>
  );
}

export function ClockOutlinedIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <g mask="url(#mask0_13104_56160)">
        <path
          d="M10.542 9.4525V5.53902C10.542 5.38556 10.4904 5.25692 10.3874 5.1531C10.2844 5.04928 10.1567 4.99738 10.0044 4.99738C9.8521 4.99738 9.72307 5.04928 9.6173 5.1531C9.51154 5.25692 9.45866 5.38556 9.45866 5.53902V9.64481C9.45866 9.74291 9.47682 9.831 9.51314 9.90908C9.54946 9.98717 9.59967 10.0583 9.66378 10.1224L12.5564 13.015C12.6739 13.1325 12.8027 13.1891 12.9426 13.1849C13.0826 13.1806 13.2129 13.1181 13.3336 12.9974C13.4544 12.8767 13.5147 12.7477 13.5147 12.6104C13.5147 12.4731 13.452 12.3423 13.3266 12.2181L10.542 9.4525ZM10.0017 17.5807C8.9571 17.5807 7.97521 17.3833 7.05603 16.9886C6.13685 16.5939 5.33134 16.0508 4.63949 15.3592C3.94763 14.6677 3.40425 13.8615 3.00935 12.9408C2.61444 12.02 2.41699 11.0361 2.41699 9.98923C2.41699 8.94231 2.61435 7.95797 3.00908 7.03619C3.4038 6.1144 3.94692 5.31105 4.63845 4.62615C5.32999 3.94123 6.13615 3.40132 7.05693 3.00642C7.97771 2.61151 8.96155 2.41406 10.0085 2.41406C11.0554 2.41406 12.0398 2.61309 12.9616 3.01115C13.8835 3.4092 14.6854 3.94941 15.3673 4.63177C16.0493 5.31415 16.5891 6.11657 16.9869 7.03904C17.3847 7.9615 17.5836 8.94715 17.5836 9.99598C17.5836 11.0406 17.3863 12.0225 16.9915 12.9416C16.5968 13.8608 16.0572 14.6663 15.3726 15.3582C14.688 16.05 13.8841 16.5934 12.9609 16.9883C12.0377 17.3832 11.0513 17.5807 10.0017 17.5807ZM10.0102 16.4974C11.8085 16.4974 13.3398 15.862 14.604 14.5911C15.8682 13.3203 16.5003 11.7857 16.5003 9.98744C16.5003 8.18915 15.8682 6.6579 14.604 5.39369C13.3398 4.12948 11.8085 3.49738 10.0102 3.49738C8.21195 3.49738 6.67739 4.12948 5.40655 5.39369C4.13572 6.6579 3.5003 8.18915 3.5003 9.98744C3.5003 11.7857 4.13572 13.3203 5.40655 14.5911C6.67739 15.862 8.21195 16.4974 10.0102 16.4974Z"
          fill="#1C1B1F"
        />
      </g>
    </svg>
  );
}

export function ScheduleMeetingNowIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="167" height="34" viewBox="0 0 167 34" fill="none">
      <rect x="0.5" y="0.5" width="166" height="33" rx="3.5" stroke="#5E6CC7" />
      <path d="M18.6875 15.8206C18.6875 15.54 18.915 15.3125 19.1956 15.3125H25.4919C25.7725 15.3125 26 15.54 26 15.8206V19.5312C26 21.5505 24.363 23.1875 22.3438 23.1875C20.3245 23.1875 18.6875 21.5505 18.6875 19.5312V15.8206Z" fill="url(#paint0_linear_9378_66915)" />
      <path d="M13.0625 14.877C13.0625 14.4962 13.3712 14.1875 13.752 14.1875H21.373C21.7538 14.1875 22.0625 14.4962 22.0625 14.877V20.375C22.0625 22.8603 20.0478 24.875 17.5625 24.875C15.0772 24.875 13.0625 22.8603 13.0625 20.375V14.877Z" fill="url(#paint1_linear_9378_66915)" />
      <circle cx="23.1875" cy="12.5" r="1.6875" fill="#34439E" />
      <circle cx="23.1875" cy="12.5" r="1.6875" fill="url(#paint2_linear_9378_66915)" />
      <circle cx="18.125" cy="11.375" r="2.25" fill="url(#paint3_linear_9378_66915)" />

      <path d="M17.5625 13.625C19.1158 13.625 20.375 12.3658 20.375 10.8125C20.375 9.25917 19.1158 8 17.5625 8C16.0092 8 14.75 9.25917 14.75 10.8125C14.75 12.3658 16.0092 13.625 17.5625 13.625Z" fill="url(#paint4_linear_9378_66915)" />
      <path d="M13.752 14.1875C13.3712 14.1875 13.0625 14.4962 13.0625 14.877V20.375C13.0625 22.8603 15.0772 24.875 17.5625 24.875C20.0478 24.875 22.0625 22.8603 22.0625 20.375V14.877C22.0625 14.4962 21.7538 14.1875 21.373 14.1875H13.752Z" fill="url(#paint5_linear_9378_66915)" />
      <g mask="url(#mask0_9378_66915)">
        <path d="M11.9375 14.75C11.9375 13.818 12.693 13.0625 13.625 13.0625H17.5625C18.4945 13.0625 19.25 13.818 19.25 14.75V21.5C19.25 22.432 18.4945 23.1875 17.5625 23.1875H11.9375V14.75Z" fill="black" fill-opacity="0.3" />
      </g>
      <rect x="8" y="11.9375" width="10.125" height="10.125" rx="1.125" fill="url(#paint6_linear_9378_66915)" />
      <path d="M15.3125 14.1875H10.8125V15.2195H12.4972V19.8125H13.6278V15.2195H15.3125V14.1875Z" fill="white" />
      <path
        d="M40.5371 18.8438C40.5371 18.6445 40.5059 18.4688 40.4434 18.3164C40.3848 18.1602 40.2793 18.0195 40.127 17.8945C39.9785 17.7695 39.7715 17.6504 39.5059 17.5371C39.2441 17.4238 38.9121 17.3086 38.5098 17.1914C38.0879 17.0664 37.707 16.9277 37.3672 16.7754C37.0273 16.6191 36.7363 16.4414 36.4941 16.2422C36.252 16.043 36.0664 15.8145 35.9375 15.5566C35.8086 15.2988 35.7441 15.0039 35.7441 14.6719C35.7441 14.3398 35.8125 14.0332 35.9492 13.752C36.0859 13.4707 36.2812 13.2266 36.5352 13.0195C36.793 12.8086 37.0996 12.6445 37.4551 12.5273C37.8105 12.4102 38.207 12.3516 38.6445 12.3516C39.2852 12.3516 39.8281 12.4746 40.2734 12.7207C40.7227 12.9629 41.0645 13.2812 41.2988 13.6758C41.5332 14.0664 41.6504 14.4844 41.6504 14.9297H40.5254C40.5254 14.6094 40.457 14.3262 40.3203 14.0801C40.1836 13.8301 39.9766 13.6348 39.6992 13.4941C39.4219 13.3496 39.0703 13.2773 38.6445 13.2773C38.2422 13.2773 37.9102 13.3379 37.6484 13.459C37.3867 13.5801 37.1914 13.7441 37.0625 13.9512C36.9375 14.1582 36.875 14.3945 36.875 14.6602C36.875 14.8398 36.9121 15.0039 36.9863 15.1523C37.0645 15.2969 37.1836 15.4316 37.3438 15.5566C37.5078 15.6816 37.7148 15.7969 37.9648 15.9023C38.2188 16.0078 38.5215 16.1094 38.873 16.207C39.3574 16.3438 39.7754 16.4961 40.127 16.6641C40.4785 16.832 40.7676 17.0215 40.9941 17.2324C41.2246 17.4395 41.3945 17.6758 41.5039 17.9414C41.6172 18.2031 41.6738 18.5 41.6738 18.832C41.6738 19.1797 41.6035 19.4941 41.4629 19.7754C41.3223 20.0566 41.1211 20.2969 40.8594 20.4961C40.5977 20.6953 40.2832 20.8496 39.916 20.959C39.5527 21.0645 39.1465 21.1172 38.6973 21.1172C38.3027 21.1172 37.9141 21.0625 37.5312 20.9531C37.1523 20.8438 36.8066 20.6797 36.4941 20.4609C36.1855 20.2422 35.9375 19.9727 35.75 19.6523C35.5664 19.3281 35.4746 18.9531 35.4746 18.5273H36.5996C36.5996 18.8203 36.6562 19.0723 36.7695 19.2832C36.8828 19.4902 37.0371 19.6621 37.2324 19.7988C37.4316 19.9355 37.6562 20.0371 37.9062 20.1035C38.1602 20.166 38.4238 20.1973 38.6973 20.1973C39.0918 20.1973 39.4258 20.1426 39.6992 20.0332C39.9727 19.9238 40.1797 19.7676 40.3203 19.5645C40.4648 19.3613 40.5371 19.1211 40.5371 18.8438ZM45.5363 20.2266C45.7941 20.2266 46.0324 20.1738 46.2511 20.0684C46.4699 19.9629 46.6496 19.8184 46.7902 19.6348C46.9308 19.4473 47.0109 19.2344 47.0304 18.9961H48.0617C48.0421 19.3711 47.9152 19.7207 47.6808 20.0449C47.4503 20.3652 47.1476 20.625 46.7726 20.8242C46.3976 21.0195 45.9855 21.1172 45.5363 21.1172C45.0597 21.1172 44.6437 21.0332 44.2882 20.8652C43.9367 20.6973 43.6437 20.4668 43.4093 20.1738C43.1789 19.8809 43.005 19.5449 42.8878 19.166C42.7746 18.7832 42.7179 18.3789 42.7179 17.9531V17.707C42.7179 17.2812 42.7746 16.8789 42.8878 16.5C43.005 16.1172 43.1789 15.7793 43.4093 15.4863C43.6437 15.1934 43.9367 14.9629 44.2882 14.7949C44.6437 14.627 45.0597 14.543 45.5363 14.543C46.0324 14.543 46.466 14.6445 46.8371 14.8477C47.2082 15.0469 47.4992 15.3203 47.7101 15.668C47.925 16.0117 48.0421 16.4023 48.0617 16.8398H47.0304C47.0109 16.5781 46.9367 16.3418 46.8078 16.1309C46.6828 15.9199 46.5109 15.752 46.2921 15.627C46.0773 15.498 45.8253 15.4336 45.5363 15.4336C45.2042 15.4336 44.925 15.5 44.6984 15.6328C44.4757 15.7617 44.298 15.9375 44.1652 16.1602C44.0363 16.3789 43.9425 16.623 43.8839 16.8926C43.8292 17.1582 43.8019 17.4297 43.8019 17.707V17.9531C43.8019 18.2305 43.8292 18.5039 43.8839 18.7734C43.9386 19.043 44.0304 19.2871 44.1593 19.5059C44.2921 19.7246 44.4699 19.9004 44.6925 20.0332C44.9191 20.1621 45.2003 20.2266 45.5363 20.2266ZM50.4124 12V21H49.3284V12H50.4124ZM50.1546 17.5898L49.7034 17.5723C49.7073 17.1387 49.7718 16.7383 49.8968 16.3711C50.0218 16 50.1976 15.6777 50.4241 15.4043C50.6507 15.1309 50.9202 14.9199 51.2327 14.7715C51.5491 14.6191 51.8987 14.543 52.2815 14.543C52.594 14.543 52.8753 14.5859 53.1253 14.6719C53.3753 14.7539 53.5882 14.8867 53.764 15.0703C53.9437 15.2539 54.0804 15.4922 54.1741 15.7852C54.2679 16.0742 54.3147 16.4277 54.3147 16.8457V21H53.2249V16.834C53.2249 16.502 53.1761 16.2363 53.0784 16.0371C52.9808 15.834 52.8382 15.6875 52.6507 15.5977C52.4632 15.5039 52.2327 15.457 51.9593 15.457C51.6897 15.457 51.4437 15.5137 51.221 15.627C51.0022 15.7402 50.8128 15.8965 50.6526 16.0957C50.4964 16.2949 50.3733 16.5234 50.2835 16.7812C50.1976 17.0352 50.1546 17.3047 50.1546 17.5898ZM58.6167 21.1172C58.1752 21.1172 57.7749 21.043 57.4155 20.8945C57.06 20.7422 56.7534 20.5293 56.4956 20.2559C56.2417 19.9824 56.0463 19.6582 55.9096 19.2832C55.7729 18.9082 55.7045 18.498 55.7045 18.0527V17.8066C55.7045 17.291 55.7807 16.832 55.9331 16.4297C56.0854 16.0234 56.2924 15.6797 56.5542 15.3984C56.8159 15.1172 57.1127 14.9043 57.4448 14.7598C57.7768 14.6152 58.1206 14.543 58.476 14.543C58.9292 14.543 59.3198 14.6211 59.6479 14.7773C59.9799 14.9336 60.2514 15.1523 60.4624 15.4336C60.6733 15.7109 60.8295 16.0391 60.9311 16.418C61.0327 16.793 61.0835 17.2031 61.0835 17.6484V18.1348H56.3491V17.25H59.9995V17.168C59.9838 16.8867 59.9252 16.6133 59.8237 16.3477C59.726 16.082 59.5698 15.8633 59.3549 15.6914C59.1401 15.5195 58.8471 15.4336 58.476 15.4336C58.2299 15.4336 58.0034 15.4863 57.7963 15.5918C57.5893 15.6934 57.4116 15.8457 57.2631 16.0488C57.1147 16.252 56.9995 16.5 56.9174 16.793C56.8354 17.0859 56.7944 17.4238 56.7944 17.8066V18.0527C56.7944 18.3535 56.8354 18.6367 56.9174 18.9023C57.0034 19.1641 57.1264 19.3945 57.2866 19.5938C57.4506 19.793 57.6479 19.9492 57.8784 20.0625C58.1127 20.1758 58.3784 20.2324 58.6752 20.2324C59.0581 20.2324 59.3823 20.1543 59.6479 19.998C59.9135 19.8418 60.146 19.6328 60.3452 19.3711L61.0014 19.8926C60.8647 20.0996 60.6909 20.2969 60.4799 20.4844C60.269 20.6719 60.0092 20.8242 59.7006 20.9414C59.396 21.0586 59.0346 21.1172 58.6167 21.1172ZM66.3932 19.7695V12H67.483V21H66.4869L66.3932 19.7695ZM62.1275 17.9004V17.7773C62.1275 17.293 62.1861 16.8535 62.3033 16.459C62.4244 16.0605 62.5943 15.7188 62.8131 15.4336C63.0357 15.1484 63.2994 14.9297 63.6041 14.7773C63.9127 14.6211 64.2565 14.543 64.6354 14.543C65.0338 14.543 65.3815 14.6133 65.6783 14.7539C65.9791 14.8906 66.233 15.0918 66.44 15.3574C66.651 15.6191 66.817 15.9355 66.9381 16.3066C67.0592 16.6777 67.1432 17.0977 67.19 17.5664V18.1055C67.1471 18.5703 67.0631 18.9883 66.9381 19.3594C66.817 19.7305 66.651 20.0469 66.44 20.3086C66.233 20.5703 65.9791 20.7715 65.6783 20.9121C65.3775 21.0488 65.026 21.1172 64.6236 21.1172C64.2525 21.1172 63.9127 21.0371 63.6041 20.877C63.2994 20.7168 63.0357 20.4922 62.8131 20.2031C62.5943 19.9141 62.4244 19.5742 62.3033 19.1836C62.1861 18.7891 62.1275 18.3613 62.1275 17.9004ZM63.2174 17.7773V17.9004C63.2174 18.2168 63.2486 18.5137 63.3111 18.791C63.3775 19.0684 63.4791 19.3125 63.6158 19.5234C63.7525 19.7344 63.9264 19.9004 64.1373 20.0215C64.3482 20.1387 64.6002 20.1973 64.8932 20.1973C65.2525 20.1973 65.5475 20.1211 65.7779 19.9688C66.0123 19.8164 66.1998 19.6152 66.3404 19.3652C66.4811 19.1152 66.5904 18.8438 66.6686 18.5508V17.1387C66.6217 16.9238 66.5533 16.7168 66.4635 16.5176C66.3775 16.3145 66.2643 16.1348 66.1236 15.9785C65.9869 15.8184 65.817 15.6914 65.6139 15.5977C65.4147 15.5039 65.1783 15.457 64.9049 15.457C64.608 15.457 64.3522 15.5195 64.1373 15.6445C63.9264 15.7656 63.7525 15.9336 63.6158 16.1484C63.4791 16.3594 63.3775 16.6055 63.3111 16.8867C63.2486 17.1641 63.2174 17.4609 63.2174 17.7773ZM73.0974 19.5352V14.6602H74.1873V21H73.1502L73.0974 19.5352ZM73.3025 18.1992L73.7537 18.1875C73.7537 18.6094 73.7088 19 73.6189 19.3594C73.533 19.7148 73.3923 20.0234 73.197 20.2852C73.0017 20.5469 72.7459 20.752 72.4295 20.9004C72.113 21.0449 71.7283 21.1172 71.2752 21.1172C70.9666 21.1172 70.6834 21.0723 70.4255 20.9824C70.1716 20.8926 69.9529 20.7539 69.7693 20.5664C69.5857 20.3789 69.4431 20.1348 69.3416 19.834C69.2439 19.5332 69.1951 19.1719 69.1951 18.75V14.6602H70.2791V18.7617C70.2791 19.0469 70.3103 19.2832 70.3728 19.4707C70.4392 19.6543 70.5271 19.8008 70.6365 19.9102C70.7498 20.0156 70.8748 20.0898 71.0115 20.1328C71.1521 20.1758 71.2966 20.1973 71.4451 20.1973C71.906 20.1973 72.2713 20.1094 72.5408 19.9336C72.8103 19.7539 73.0037 19.5137 73.1209 19.2129C73.242 18.9082 73.3025 18.5703 73.3025 18.1992ZM77.0653 12V21H75.9755V12H77.0653ZM81.4844 21.1172C81.043 21.1172 80.6426 21.043 80.2833 20.8945C79.9278 20.7422 79.6212 20.5293 79.3633 20.2559C79.1094 19.9824 78.9141 19.6582 78.7774 19.2832C78.6407 18.9082 78.5723 18.498 78.5723 18.0527V17.8066C78.5723 17.291 78.6485 16.832 78.8008 16.4297C78.9532 16.0234 79.1602 15.6797 79.4219 15.3984C79.6837 15.1172 79.9805 14.9043 80.3126 14.7598C80.6446 14.6152 80.9883 14.543 81.3438 14.543C81.7969 14.543 82.1876 14.6211 82.5157 14.7773C82.8477 14.9336 83.1192 15.1523 83.3301 15.4336C83.5411 15.7109 83.6973 16.0391 83.7989 16.418C83.9005 16.793 83.9512 17.2031 83.9512 17.6484V18.1348H79.2169V17.25H82.8672V17.168C82.8516 16.8867 82.793 16.6133 82.6915 16.3477C82.5938 16.082 82.4376 15.8633 82.2227 15.6914C82.0079 15.5195 81.7149 15.4336 81.3438 15.4336C81.0977 15.4336 80.8712 15.4863 80.6641 15.5918C80.4571 15.6934 80.2794 15.8457 80.1309 16.0488C79.9825 16.252 79.8672 16.5 79.7852 16.793C79.7032 17.0859 79.6622 17.4238 79.6622 17.8066V18.0527C79.6622 18.3535 79.7032 18.6367 79.7852 18.9023C79.8712 19.1641 79.9942 19.3945 80.1544 19.5938C80.3184 19.793 80.5157 19.9492 80.7462 20.0625C80.9805 20.1758 81.2462 20.2324 81.543 20.2324C81.9258 20.2324 82.2501 20.1543 82.5157 19.998C82.7813 19.8418 83.0137 19.6328 83.213 19.3711L83.8692 19.8926C83.7325 20.0996 83.5587 20.2969 83.3477 20.4844C83.1368 20.6719 82.877 20.8242 82.5684 20.9414C82.2637 21.0586 81.9024 21.1172 81.4844 21.1172ZM88.8109 12.4688H89.9066L92.7015 19.4238L95.4906 12.4688H96.5922L93.1234 21H92.2679L88.8109 12.4688ZM88.4535 12.4688H89.4203L89.5785 17.6719V21H88.4535V12.4688ZM95.9769 12.4688H96.9437V21H95.8187V17.6719L95.9769 12.4688ZM101.445 21.1172C101.003 21.1172 100.603 21.043 100.244 20.8945C99.8882 20.7422 99.5816 20.5293 99.3237 20.2559C99.0698 19.9824 98.8745 19.6582 98.7378 19.2832C98.6011 18.9082 98.5327 18.498 98.5327 18.0527V17.8066C98.5327 17.291 98.6089 16.832 98.7612 16.4297C98.9136 16.0234 99.1206 15.6797 99.3823 15.3984C99.6441 15.1172 99.9409 14.9043 100.273 14.7598C100.605 14.6152 100.949 14.543 101.304 14.543C101.757 14.543 102.148 14.6211 102.476 14.7773C102.808 14.9336 103.08 15.1523 103.291 15.4336C103.501 15.7109 103.658 16.0391 103.759 16.418C103.861 16.793 103.912 17.2031 103.912 17.6484V18.1348H99.1773V17.25H102.828V17.168C102.812 16.8867 102.753 16.6133 102.652 16.3477C102.554 16.082 102.398 15.8633 102.183 15.6914C101.968 15.5195 101.675 15.4336 101.304 15.4336C101.058 15.4336 100.832 15.4863 100.625 15.5918C100.417 15.6934 100.24 15.8457 100.091 16.0488C99.9429 16.252 99.8277 16.5 99.7456 16.793C99.6636 17.0859 99.6226 17.4238 99.6226 17.8066V18.0527C99.6226 18.3535 99.6636 18.6367 99.7456 18.9023C99.8316 19.1641 99.9546 19.3945 100.115 19.5938C100.279 19.793 100.476 19.9492 100.707 20.0625C100.941 20.1758 101.207 20.2324 101.503 20.2324C101.886 20.2324 102.21 20.1543 102.476 19.998C102.742 19.8418 102.974 19.6328 103.173 19.3711L103.83 19.8926C103.693 20.0996 103.519 20.2969 103.308 20.4844C103.097 20.6719 102.837 20.8242 102.529 20.9414C102.224 21.0586 101.863 21.1172 101.445 21.1172ZM107.856 21.1172C107.415 21.1172 107.014 21.043 106.655 20.8945C106.299 20.7422 105.993 20.5293 105.735 20.2559C105.481 19.9824 105.286 19.6582 105.149 19.2832C105.012 18.9082 104.944 18.498 104.944 18.0527V17.8066C104.944 17.291 105.02 16.832 105.173 16.4297C105.325 16.0234 105.532 15.6797 105.794 15.3984C106.055 15.1172 106.352 14.9043 106.684 14.7598C107.016 14.6152 107.36 14.543 107.715 14.543C108.169 14.543 108.559 14.6211 108.887 14.7773C109.219 14.9336 109.491 15.1523 109.702 15.4336C109.913 15.7109 110.069 16.0391 110.171 16.418C110.272 16.793 110.323 17.2031 110.323 17.6484V18.1348H105.589V17.25H109.239V17.168C109.223 16.8867 109.165 16.6133 109.063 16.3477C108.965 16.082 108.809 15.8633 108.594 15.6914C108.38 15.5195 108.087 15.4336 107.715 15.4336C107.469 15.4336 107.243 15.4863 107.036 15.5918C106.829 15.6934 106.651 15.8457 106.503 16.0488C106.354 16.252 106.239 16.5 106.157 16.793C106.075 17.0859 106.034 17.4238 106.034 17.8066V18.0527C106.034 18.3535 106.075 18.6367 106.157 18.9023C106.243 19.1641 106.366 19.3945 106.526 19.5938C106.69 19.793 106.887 19.9492 107.118 20.0625C107.352 20.1758 107.618 20.2324 107.915 20.2324C108.298 20.2324 108.622 20.1543 108.887 19.998C109.153 19.8418 109.385 19.6328 109.585 19.3711L110.241 19.8926C110.104 20.0996 109.93 20.2969 109.719 20.4844C109.508 20.6719 109.249 20.8242 108.94 20.9414C108.635 21.0586 108.274 21.1172 107.856 21.1172ZM114.291 14.6602V15.4922H110.863V14.6602H114.291ZM112.023 13.1191H113.107V19.4297C113.107 19.6445 113.14 19.8066 113.207 19.916C113.273 20.0254 113.359 20.0977 113.465 20.1328C113.57 20.168 113.683 20.1855 113.805 20.1855C113.894 20.1855 113.988 20.1777 114.086 20.1621C114.187 20.1426 114.263 20.127 114.314 20.1152L114.32 21C114.234 21.0273 114.121 21.0527 113.98 21.0762C113.844 21.1035 113.678 21.1172 113.482 21.1172C113.217 21.1172 112.972 21.0645 112.75 20.959C112.527 20.8535 112.349 20.6777 112.217 20.4316C112.088 20.1816 112.023 19.8457 112.023 19.4238V13.1191ZM116.788 14.6602V21H115.698V14.6602H116.788ZM115.616 12.9785C115.616 12.8027 115.669 12.6543 115.774 12.5332C115.884 12.4121 116.044 12.3516 116.255 12.3516C116.462 12.3516 116.62 12.4121 116.729 12.5332C116.843 12.6543 116.899 12.8027 116.899 12.9785C116.899 13.1465 116.843 13.291 116.729 13.4121C116.62 13.5293 116.462 13.5879 116.255 13.5879C116.044 13.5879 115.884 13.5293 115.774 13.4121C115.669 13.291 115.616 13.1465 115.616 12.9785ZM119.66 16.0137V21H118.576V14.6602H119.602L119.66 16.0137ZM119.402 17.5898L118.951 17.5723C118.955 17.1387 119.02 16.7383 119.145 16.3711C119.27 16 119.445 15.6777 119.672 15.4043C119.899 15.1309 120.168 14.9199 120.481 14.7715C120.797 14.6191 121.147 14.543 121.529 14.543C121.842 14.543 122.123 14.5859 122.373 14.6719C122.623 14.7539 122.836 14.8867 123.012 15.0703C123.192 15.2539 123.328 15.4922 123.422 15.7852C123.516 16.0742 123.563 16.4277 123.563 16.8457V21H122.473V16.834C122.473 16.502 122.424 16.2363 122.326 16.0371C122.229 15.834 122.086 15.6875 121.899 15.5977C121.711 15.5039 121.481 15.457 121.207 15.457C120.938 15.457 120.692 15.5137 120.469 15.627C120.25 15.7402 120.061 15.8965 119.901 16.0957C119.744 16.2949 119.621 16.5234 119.531 16.7812C119.445 17.0352 119.402 17.3047 119.402 17.5898ZM129.365 14.6602H130.349V20.8652C130.349 21.4238 130.236 21.9004 130.009 22.2949C129.783 22.6895 129.466 22.9883 129.06 23.1914C128.658 23.3984 128.193 23.502 127.665 23.502C127.447 23.502 127.189 23.4668 126.892 23.3965C126.599 23.3301 126.31 23.2148 126.025 23.0508C125.743 22.8906 125.507 22.6738 125.316 22.4004L125.884 21.7559C126.15 22.0762 126.427 22.2988 126.716 22.4238C127.009 22.5488 127.298 22.6113 127.583 22.6113C127.927 22.6113 128.224 22.5469 128.474 22.418C128.724 22.2891 128.917 22.0977 129.054 21.8438C129.195 21.5938 129.265 21.2852 129.265 20.918V16.0547L129.365 14.6602ZM124.999 17.9004V17.7773C124.999 17.293 125.056 16.8535 125.169 16.459C125.286 16.0605 125.452 15.7188 125.667 15.4336C125.886 15.1484 126.15 14.9297 126.458 14.7773C126.767 14.6211 127.115 14.543 127.501 14.543C127.9 14.543 128.247 14.6133 128.544 14.7539C128.845 14.8906 129.099 15.0918 129.306 15.3574C129.517 15.6191 129.683 15.9355 129.804 16.3066C129.925 16.6777 130.009 17.0977 130.056 17.5664V18.1055C130.013 18.5703 129.929 18.9883 129.804 19.3594C129.683 19.7305 129.517 20.0469 129.306 20.3086C129.099 20.5703 128.845 20.7715 128.544 20.9121C128.243 21.0488 127.892 21.1172 127.49 21.1172C127.111 21.1172 126.767 21.0371 126.458 20.877C126.154 20.7168 125.892 20.4922 125.673 20.2031C125.454 19.9141 125.286 19.5742 125.169 19.1836C125.056 18.7891 124.999 18.3613 124.999 17.9004ZM126.083 17.7773V17.9004C126.083 18.2168 126.115 18.5137 126.177 18.791C126.243 19.0684 126.343 19.3125 126.476 19.5234C126.613 19.7344 126.786 19.9004 126.997 20.0215C127.208 20.1387 127.46 20.1973 127.753 20.1973C128.113 20.1973 128.409 20.1211 128.644 19.9688C128.878 19.8164 129.064 19.6152 129.2 19.3652C129.341 19.1152 129.45 18.8438 129.529 18.5508V17.1387C129.486 16.9238 129.419 16.7168 129.329 16.5176C129.243 16.3145 129.13 16.1348 128.99 15.9785C128.853 15.8184 128.683 15.6914 128.48 15.5977C128.277 15.5039 128.038 15.457 127.765 15.457C127.468 15.457 127.212 15.5195 126.997 15.6445C126.786 15.7656 126.613 15.9336 126.476 16.1484C126.343 16.3594 126.243 16.6055 126.177 16.8867C126.115 17.1641 126.083 17.4609 126.083 17.7773ZM141.795 12.4688V21H140.658L136.363 14.4199V21H135.232V12.4688H136.363L140.675 19.0664V12.4688H141.795ZM143.395 17.9004V17.7656C143.395 17.3086 143.462 16.8848 143.594 16.4941C143.727 16.0996 143.919 15.7578 144.169 15.4688C144.419 15.1758 144.721 14.9492 145.077 14.7891C145.432 14.625 145.831 14.543 146.272 14.543C146.718 14.543 147.118 14.625 147.473 14.7891C147.833 14.9492 148.137 15.1758 148.387 15.4688C148.641 15.7578 148.835 16.0996 148.968 16.4941C149.1 16.8848 149.167 17.3086 149.167 17.7656V17.9004C149.167 18.3574 149.1 18.7812 148.968 19.1719C148.835 19.5625 148.641 19.9043 148.387 20.1973C148.137 20.4863 147.835 20.7129 147.479 20.877C147.128 21.0371 146.729 21.1172 146.284 21.1172C145.839 21.1172 145.438 21.0371 145.083 20.877C144.727 20.7129 144.423 20.4863 144.169 20.1973C143.919 19.9043 143.727 19.5625 143.594 19.1719C143.462 18.7812 143.395 18.3574 143.395 17.9004ZM144.479 17.7656V17.9004C144.479 18.2168 144.516 18.5156 144.591 18.7969C144.665 19.0742 144.776 19.3203 144.925 19.5352C145.077 19.75 145.266 19.9199 145.493 20.0449C145.719 20.166 145.983 20.2266 146.284 20.2266C146.581 20.2266 146.841 20.166 147.063 20.0449C147.29 19.9199 147.477 19.75 147.626 19.5352C147.774 19.3203 147.885 19.0742 147.96 18.7969C148.038 18.5156 148.077 18.2168 148.077 17.9004V17.7656C148.077 17.4531 148.038 17.1582 147.96 16.8809C147.885 16.5996 147.772 16.3516 147.62 16.1367C147.471 15.918 147.284 15.7461 147.057 15.6211C146.835 15.4961 146.573 15.4336 146.272 15.4336C145.975 15.4336 145.714 15.4961 145.487 15.6211C145.264 15.7461 145.077 15.918 144.925 16.1367C144.776 16.3516 144.665 16.5996 144.591 16.8809C144.516 17.1582 144.479 17.4531 144.479 17.7656ZM152.18 19.875L153.808 14.6602H154.523L154.383 15.6973L152.725 21H152.027L152.18 19.875ZM151.084 14.6602L152.473 19.9336L152.572 21H151.84L150 14.6602H151.084ZM156.082 19.8926L157.406 14.6602H158.484L156.644 21H155.918L156.082 19.8926ZM154.682 14.6602L156.275 19.7871L156.457 21H155.766L154.06 15.6855L153.92 14.6602H154.682Z"
        fill="#5E6CC7"
      />
      <defs>
        <linearGradient id="paint0_linear_9378_66915" x1="18.6875" y1="15.727" x2="26.0895" y2="20.5637" gradientUnits="userSpaceOnUse">
          <stop stop-color="#364088" />
          <stop offset="1" stop-color="#6E7EE1" />
        </linearGradient>
        <linearGradient id="paint1_linear_9378_66915" x1="13.0625" y1="18.9147" x2="22.0625" y2="18.9147" gradientUnits="userSpaceOnUse">
          <stop stop-color="#515FC4" />
          <stop offset="1" stop-color="#7084EA" />
        </linearGradient>
        <linearGradient id="paint2_linear_9378_66915" x1="21.5" y1="10.9901" x2="24.7604" y2="13.2845" gradientUnits="userSpaceOnUse">
          <stop stop-color="#364088" />
          <stop offset="1" stop-color="#6E7EE1" />
        </linearGradient>
        <linearGradient id="paint3_linear_9378_66915" x1="16.5179" y1="9.76786" x2="19.4107" y2="13.1429" gradientUnits="userSpaceOnUse">
          <stop stop-color="#4858AE" />
          <stop offset="1" stop-color="#4E60CE" />
        </linearGradient>
        <linearGradient id="paint4_linear_9378_66915" x1="15.5536" y1="8.80357" x2="19.1696" y2="13.0223" gradientUnits="userSpaceOnUse">
          <stop stop-color="#4858AE" />
          <stop offset="1" stop-color="#4E60CE" />
        </linearGradient>
        <linearGradient id="paint5_linear_9378_66915" x1="15.5536" y1="8.80357" x2="19.1696" y2="13.0223" gradientUnits="userSpaceOnUse">
          <stop stop-color="#4858AE" />
          <stop offset="1" stop-color="#4E60CE" />
        </linearGradient>
        <linearGradient id="paint6_linear_9378_66915" x1="8" y1="17" x2="18.125" y2="17" gradientUnits="userSpaceOnUse">
          <stop stop-color="#2A3887" />
          <stop offset="1" stop-color="#4C56B9" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export function MsTeamsIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path d="M10.6875 7.82056C10.6875 7.53997 10.915 7.3125 11.1956 7.3125H17.4919C17.7725 7.3125 18 7.53997 18 7.82056V11.5312C18 13.5505 16.363 15.1875 14.3438 15.1875C12.3245 15.1875 10.6875 13.5505 10.6875 11.5312V7.82056Z" fill="url(#paint0_linear_9378_66917)" />
      <path d="M5.0625 6.87702C5.0625 6.49621 5.37121 6.1875 5.75202 6.1875H13.373C13.7538 6.1875 14.0625 6.49621 14.0625 6.87702V12.375C14.0625 14.8603 12.0478 16.875 9.5625 16.875C7.07722 16.875 5.0625 14.8603 5.0625 12.375V6.87702Z" fill="url(#paint1_linear_9378_66917)" />
      <circle cx="15.1875" cy="4.5" r="1.6875" fill="#34439E" />
      <circle cx="15.1875" cy="4.5" r="1.6875" fill="url(#paint2_linear_9378_66917)" />
      <circle cx="10.125" cy="3.375" r="2.25" fill="url(#paint3_linear_9378_66917)" />
      <path d="M9.5625 5.625C11.1158 5.625 12.375 4.36583 12.375 2.8125C12.375 1.25917 11.1158 0 9.5625 0C8.00917 0 6.75 1.25917 6.75 2.8125C6.75 4.36583 8.00917 5.625 9.5625 5.625Z" fill="url(#paint4_linear_9378_66917)" />
      <path d="M5.75203 6.1875C5.37122 6.1875 5.0625 6.49622 5.0625 6.87703V12.375C5.0625 14.8603 7.07719 16.875 9.5625 16.875C12.0478 16.875 14.0625 14.8603 14.0625 12.375V6.87703C14.0625 6.49622 13.7538 6.1875 13.373 6.1875H5.75203Z" fill="url(#paint5_linear_9378_66917)" />
      <g mask="url(#mask0_9378_66917)">
        <path d="M3.9375 6.75C3.9375 5.81802 4.69302 5.0625 5.625 5.0625H9.5625C10.4945 5.0625 11.25 5.81802 11.25 6.75V13.5C11.25 14.432 10.4945 15.1875 9.5625 15.1875H3.9375V6.75Z" fill="black" fill-opacity="0.3" />
      </g>
      <rect y="3.9375" width="10.125" height="10.125" rx="1.125" fill="url(#paint6_linear_9378_66917)" />
      <path d="M7.3125 6.1875H2.8125V7.21954H4.49715V11.8125H5.62785V7.21954H7.3125V6.1875Z" fill="white" />
      <defs>
        <linearGradient id="paint0_linear_9378_66917" x1="10.6875" y1="7.72697" x2="18.0895" y2="12.5637" gradientUnits="userSpaceOnUse">
          <stop stop-color="#364088" />
          <stop offset="1" stop-color="#6E7EE1" />
        </linearGradient>
        <linearGradient id="paint1_linear_9378_66917" x1="5.0625" y1="10.9147" x2="14.0625" y2="10.9147" gradientUnits="userSpaceOnUse">
          <stop stop-color="#515FC4" />
          <stop offset="1" stop-color="#7084EA" />
        </linearGradient>
        <linearGradient id="paint2_linear_9378_66917" x1="13.5" y1="2.99013" x2="16.7604" y2="5.28451" gradientUnits="userSpaceOnUse">
          <stop stop-color="#364088" />
          <stop offset="1" stop-color="#6E7EE1" />
        </linearGradient>
        <linearGradient id="paint3_linear_9378_66917" x1="8.51786" y1="1.76786" x2="11.4107" y2="5.14286" gradientUnits="userSpaceOnUse">
          <stop stop-color="#4858AE" />
          <stop offset="1" stop-color="#4E60CE" />
        </linearGradient>
        <linearGradient id="paint4_linear_9378_66917" x1="7.55357" y1="0.803571" x2="11.1696" y2="5.02232" gradientUnits="userSpaceOnUse">
          <stop stop-color="#4858AE" />
          <stop offset="1" stop-color="#4E60CE" />
        </linearGradient>
        <linearGradient id="paint5_linear_9378_66917" x1="7.55357" y1="0.803571" x2="11.1696" y2="5.02232" gradientUnits="userSpaceOnUse">
          <stop stop-color="#4858AE" />
          <stop offset="1" stop-color="#4E60CE" />
        </linearGradient>
        <linearGradient id="paint6_linear_9378_66917" x1="-2.93366e-08" y1="9" x2="10.125" y2="9" gradientUnits="userSpaceOnUse">
          <stop stop-color="#2A3887" />
          <stop offset="1" stop-color="#4C56B9" />
        </linearGradient>
      </defs>
    </svg>
  );
}
export function LinkedInIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <rect x="1.125" y="1.125" width="15.75" height="15.75" rx="7.875" fill="#1275B1" />
      <path d="M7.09794 5.45183C7.09794 5.97752 6.64229 6.40367 6.08022 6.40367C5.51815 6.40367 5.0625 5.97752 5.0625 5.45183C5.0625 4.92615 5.51815 4.5 6.08022 4.5C6.64229 4.5 7.09794 4.92615 7.09794 5.45183Z" fill="white" />
      <path d="M5.20168 7.10331H6.94137V12.375H5.20168V7.10331Z" fill="white" />
      <path d="M9.74227 7.10331H8.00258V12.375H9.74227C9.74227 12.375 9.74227 10.7154 9.74227 9.67773C9.74227 9.05491 9.95493 8.42936 10.8035 8.42936C11.7624 8.42936 11.7567 9.24442 11.7522 9.87586C11.7464 10.7012 11.7603 11.5435 11.7603 12.375H13.5V9.59272C13.4853 7.81615 13.0223 6.99755 11.4994 6.99755C10.5949 6.99755 10.0343 7.40816 9.74227 7.77965V7.10331Z" fill="white" />
    </svg>
  );
}