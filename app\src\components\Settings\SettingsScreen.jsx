// SettingsScreen.jsx
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import SettingsSidebar from "./SettingsSidebar";
import ThemePreference from './ThemePreference';
import LanguageSettings from './LanguageSettings';

const SettingsScreen = () => {
  const [activeMenuItem, setActiveMenuItem] = useState('theme');

  const renderContent = () => {
    switch (activeMenuItem) {
      case 'theme':
        return <ThemePreference />;
        case 'Language':
        return <LanguageSettings/>;
      default:
        return <Typography variant="body1" sx={{ mt: 4 }}>Coming soon...</Typography>;
    }
  };

  return (
    <Box sx={{ display: 'flex', maxHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      <SettingsSidebar activeItem={activeMenuItem} onChange={setActiveMenuItem} />
      <Box sx={{ flex: 1, p: 4 }}>{renderContent()}</Box>
    </Box>
  );
};

export default SettingsScreen;
