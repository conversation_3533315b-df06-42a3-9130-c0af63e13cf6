import React from "react";

import { makeStyles } from "@mui/styles";
import Dialog from "@mui/material/Dialog";
import IconButton from "@mui/material/IconButton";
import Slide from "@mui/material/Slide";
import DialogContent from "@mui/material/DialogContent";
import Icon from "@cw/cherrywork-rds-components/Icon";
import Tooltip from "@cw/cherrywork-rds-components/Tooltip";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const useStyles = makeStyles((theme) => ({
  dialogWrapper: {
    "& .MuiDialog-paper": {
      backgroundColor: "transparent",
    },
  },
  dialogContent: {
    position: "absolute",
    top: "52px",
    height: "calc(100vh - 52px)",
    width: "100%",
    borderRadius: theme.spacing(2.5, 2.5, 0, 0),
    background: theme.palette.common.white,
  },
  closeButton: {
    position: "absolute",
    top: theme.spacing(0.75),
    right: theme.spacing(2),
  },
}));
function FullScreenDialogContainer(props) {
  const classes = useStyles();
  const handleCloseClick = () => {
    props.handleCloseSettings();
  };
  return (
    <>
      <Dialog fullScreen open={props?.open} onClose={handleCloseClick} TransitionComponent={Transition} className={classes.dialogWrapper}>
        <Tooltip title="Close" >
          <IconButton edge="start" color="inherit" onClick={handleCloseClick} aria-label="close" className={classes.closeButton}>
            <Icon
              icon={"MaterialIcon.MdOutlineClose"}
              hoverIcon={"MaterialIcon.MdClose"}
              style={{
                color: "#fff",
              }}
            />
          </IconButton>
        </Tooltip>
        <DialogContent className={classes.dialogContent} sx={{ padding: 0, background: "rgba(29, 29, 17, 0.8)" }}>
          {props?.open ? props?.component : <></>}
        </DialogContent>
      </Dialog>
    </>
  );
}

export default FullScreenDialogContainer;
