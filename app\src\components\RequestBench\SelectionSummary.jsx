import React, { useState } from "react";
import { <PERSON>, Typo<PERSON>, <PERSON>ton, Divider, Dialog, DialogTitle, DialogContent, IconButton, TableContainer, Table, TableHead, TableBody, TableRow, TableCell, Chip } from "@mui/material";
import WarningIcon from "@mui/icons-material/Warning";
import CloseIcon from "@mui/icons-material/Close";
import ViewDetailsIcon from "@mui/icons-material/Visibility";
import { REQUEST_STATUS } from "../../constant/enum";
import { colors } from "../../constant/colors";

const SelectionSummary = ({ selectedRows, count, tableData, handleMassCancel }) => {
  const [showDetails, setShowDetails] = useState(false);

  if (selectedRows.length === 0) return null;

  const selectedRequests = tableData.filter((row) => selectedRows.includes(row.id));
  const isExceededLimit = selectedRows.length > 10;

  return (
    <>
      <Paper
        elevation={3}
        sx={{
          position: "fixed",
          bottom: 20,
          left: "50%",
          transform: "translateX(-50%)",
          zIndex: 1000,
          padding: "8px 16px",
          backgroundColor: isExceededLimit ? colors.error.deepRed : colors.primary.lightPlus,
          color: "white",
          borderRadius: "24px",
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Typography sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {isExceededLimit && <WarningIcon fontSize="small" />}
          {selectedRows.length} out of {count} selected
          {isExceededLimit && (
            <Typography component="span" sx={{ ml: 1, fontSize: "0.875rem" }}>
              (Maximum 10 requests allowed)
            </Typography>
          )}
        </Typography>
        <Divider orientation="vertical" flexItem sx={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }} />
        <Button
          variant="text"
          onClick={() => setShowDetails(true)}
          sx={{
            color: "white",
            textTransform: "none",
            fontSize: "0.875rem",
            fontWeight: 500,
            padding: "4px 8px",
            minWidth: "auto",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
          startIcon={<ViewDetailsIcon />}
        >
          View Details
        </Button>
        <Divider orientation="vertical" flexItem sx={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }} />
        <Button
          variant="text"
          onClick={handleMassCancel}
          disabled={isExceededLimit}
          sx={{
            color: colors.primary.white,
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
            "&.Mui-disabled": {
              color: "rgba(255, 255, 255, 0.5)",
            },
          }}
        >
          Cancel Requests
        </Button>
      </Paper>

      <Dialog open={showDetails} onClose={() => setShowDetails(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Selected Requests
          <IconButton
            aria-label="close"
            onClick={() => setShowDetails(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Request ID</TableCell>
                  <TableCell>Request Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Priority</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {selectedRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>{request.requestId}</TableCell>
                    <TableCell>{request.requestType}</TableCell>
                    <TableCell>
                      <Chip
                        size="small"
                        label={request.reqStatus}
                        sx={{
                          background: colors.statusColorMap[request.reqStatus.toLowerCase().replace(/[^a-z0-9]/gi, "")] || colors.statusColorMap.default,
                        }}
                      />
                    </TableCell>
                    <TableCell>{request.requestPriority}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SelectionSummary;
