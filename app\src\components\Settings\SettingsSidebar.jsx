// SettingsSidebar.jsx
import React from 'react';
import { Box, Typography, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Paper } from '@mui/material';
import { Settings as SettingsIcon, AccountCircle, Palette, LocationOn, Notifications, Email, Phone, Group } from '@mui/icons-material';

const menuItems = [
  { id: 'theme', label: 'Theme Preference', icon: <Palette /> },
  { id: 'Language', label: 'Language Preference', icon: <AccountCircle /> },
  { id: 'region', label: 'Region & Timezone', icon: <LocationOn /> },
  { id: 'notifications', label: 'Notification Settings', icon: <Notifications /> },
];

const SettingsSidebar = ({ activeItem, onChange }) => (
  <Paper elevation={0} sx={{ width: 280, backgroundColor: '#ffffff', borderRight: '2px solid #e0e0e0', position: 'sticky',  }}>
    <Box sx={{ p: 3, borderBottom: '2px solid #e0e0e0' }}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <SettingsIcon sx={{ mr: 2, color: '#666' }} />
        <Typography variant="h5" sx={{ fontWeight: 600, color: '#333' }}>Settings</Typography>
      </Box>
    </Box>

    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle2" sx={{ color: '#666', mb: 2, px: 2, textTransform: 'uppercase', letterSpacing: '0.05em', fontWeight: 500 }}>Menu</Typography>
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
            <ListItemButton
              selected={activeItem === item.id}
              onClick={() => onChange(item.id)}
              sx={{
                borderLeft: 4,
                borderLeftColor: activeItem === item.id ? '#1976d2' : 'transparent',
                '&.Mui-selected': {
                  backgroundColor: '#e3f2fd',
                  color: '#1976d2',
                  '& .MuiListItemIcon-root': { color: '#1976d2' }
                },
                '&:hover': { borderLeftColor: '#bbb' }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
              <ListItemText primary={item.label} sx={{ '& .MuiTypography-root': { fontSize: '14px', fontWeight: activeItem === item.id ? 500 : 400 } }} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  </Paper>
);

export default SettingsSidebar;
