import { useState } from "react";
import { useTheme } from "@mui/material/styles";
import LeaderboardOutlinedIcon from "@mui/icons-material/LeaderboardOutlined";
import ShowChartOutlined from "@mui/icons-material/ShowChartOutlined";
import PieChartOutlineOutlinedIcon from "@mui/icons-material/PieChartOutlineOutlined";
import AlignHorizontalLeftOutlinedIcon from "@mui/icons-material/AlignHorizontalLeftOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import TableChartOutlinedIcon from "@mui/icons-material/TableChartOutlined";
import StackedBarChartOutlinedIcon from '@mui/icons-material/StackedBarChartOutlined';
const ChartButton = ({
  uiColors,
  setUiColors,
  type,
  bar,
  setBar,
  line,
  setLine,
  isStacked=false,
  isPie = false,
  isHorizontal = false,
  isMulti = false,
  isTable = false,
  isTable2=false,
  isTable3 =false,
  
}) => {
  const theme = useTheme();

  const handleBar = () => {
    setUiColors({
      ...uiColors,
      [type]: { bar: theme.palette.primary.main, line: "#9c9d9f" },
    });
    setBar({ ...bar, [type]: true });
    setLine({ ...line, [type]: false });
  };

  const handleLine = () => {
    setUiColors({
      ...uiColors,
      [type]: { line: theme.palette.primary.main, bar: "#9c9d9f" },
    });
    setBar({ ...bar, [type]: false });
    setLine({ ...line, [type]: true });
  };

  return (
    <>
      {isPie ? (
        <PieChartOutlineOutlinedIcon sx={{ color: theme.palette.primary.main }} />
      ) : isMulti ? (
        <AssessmentOutlinedIcon sx={{ color: theme.palette.primary.main }} />
      ) : isStacked ?
      (
        <StackedBarChartOutlinedIcon sx={{ color: theme.palette.primary.main }} />

      ) :(isTable || isTable2 || isTable3 )? (
        <TableChartOutlinedIcon sx={{ color: theme.palette.primary.main }} />
      ) : (
        <>
          {isHorizontal ? (
            <AlignHorizontalLeftOutlinedIcon
              onClick={handleBar}
              sx={{ color: `${bar[type] ? theme.palette.primary.main : uiColors[type].bar}` }}
            />
          ) : (
            <LeaderboardOutlinedIcon
              onClick={handleBar}
              sx={{ color: `${bar[type] ? theme.palette.primary.main : uiColors[type].bar}` }}
            />
          )}
          <ShowChartOutlined
            onClick={handleLine}
            sx={{ color: `${line[type] ? theme.palette.primary.main : uiColors[type].line}` }}
          />
        </>
      )}
    </>
  );
};

export default ChartButton;
