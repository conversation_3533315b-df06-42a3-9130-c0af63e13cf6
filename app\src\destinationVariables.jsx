// let destination = 'http://localhost:8888'
// let destination_Po = 'http://localhost:8888'

let destination_Admin = 'cw-mdg-admin-dest'
let destination_ServiceRequest = "cw-scp-serv-req-oauth2-dev"
let destination_Po ="cw-scp-purch-order-oauth2-dev"
let destination_Invoice = "cw-scp-invoice-oauth2-dev"
let destination_DocumentManagement = 'cw-mdg-documentmanagement-dest'
let destination_Returns = "cw-scp-returns-oauth2-dev"
let destination_ManageAccount = "cw-scp-manage-acct-oauth2-dev"
let destination_Notification = "cw-scp-notification-oauth2-dev"
let destination_PR = "cw-scp-pr-oauth2-dev"
let destination_IWA = "cw-mdg-iwm-dev"
let destination_IWA_NPI = "cw-mdg-iwa-oauth2-dest"
let destination_ServiceEntrySheet = "cw-scp-ses-oauth2-dev"
let destination_PlanningManagement = "cw-scp-pfm-oauth2-dev"
let destination_ITM = "ITMJavaServices"
// let destination_ITMJAVA = "cw-mdg-iwm-dev"
let destination_MaterialMgmt = "cw-mdg-materialmanagement-dest"
let destination_AI = "cw-mdg-artificialintelligence-dest"
let destination_Websocket = "cw-mdg-notification-dest"
let destination_CostCenter = "cw-mdg-costcenter-dest"
let destination_ProfitCenter = "cw-mdg-profitcenter-dest"
let destination_BankKey = "cw-mdg-bankkey-dest"
let destination_GeneralLedger = "cw-mdg-generalledger-dest"
let destination_Dashboard = "cw-mdg-dashboard-dev"
let destination_SLA_Mgmt = "cw-mdg-slamanagement-dest"
let destination_IDM = "cw-caf-idm-services"
let destination_CostCenter_Mass = ""
let destination_GeneralLedger_Mass = ""
let destination_ProfitCenter_Mass = ""

export {
    destination_Admin,
    destination_ServiceRequest,
    destination_Po,
    destination_Invoice,
    destination_Dashboard,
    destination_DocumentManagement,
    destination_Returns,
    destination_ManageAccount,
    destination_Notification,
    destination_PR,
    destination_IWA,
    destination_IWA_NPI,
    destination_ServiceEntrySheet,
    destination_PlanningManagement,
    destination_ITM,
    // destination_ITMJAVA,
    destination_MaterialMgmt,
    destination_CostCenter,
    destination_ProfitCenter,
    destination_BankKey,
    destination_GeneralLedger,
    destination_SLA_Mgmt ,
    destination_IDM,
    destination_Websocket,
    destination_AI,
    destination_CostCenter_Mass,
    destination_GeneralLedger_Mass,
    destination_ProfitCenter_Mass,
}