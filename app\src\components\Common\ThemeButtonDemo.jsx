import React from 'react';
import { 
  Button, 
  ButtonGroup,
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent,
  useTheme,
  Stack
} from '@mui/material';
import { Add, Save, Delete, Edit, Settings } from '@mui/icons-material';

const ThemeButtonDemo = () => {
  const theme = useTheme();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        MUI Button Theme Integration Demo
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3, color: theme.palette.text.secondary }}>
        All buttons below automatically use colors from your theme configuration.
        Switch themes to see the colors change dynamically!
      </Typography>

      {/* Primary Buttons */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Primary Buttons (variant="contained" color="primary")
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <Button variant="contained" color="primary">
              Default Primary
            </Button>
            <Button variant="contained" color="primary" size="small">
              Small Primary
            </Button>
            <Button variant="contained" color="primary" size="large">
              Large Primary
            </Button>
            <Button variant="contained" color="primary" startIcon={<Add />}>
              With Icon
            </Button>
            <Button variant="contained" color="primary" disabled>
              Disabled
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Secondary Buttons */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Secondary Buttons (variant="contained" color="secondary")
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <Button variant="contained" color="secondary">
              Secondary
            </Button>
            <Button variant="contained" color="secondary" startIcon={<Save />}>
              Save
            </Button>
            <Button variant="contained" color="secondary" startIcon={<Edit />}>
              Edit
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Outlined Buttons */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Outlined Buttons (variant="outlined")
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <Button variant="outlined" color="primary">
              Primary Outlined
            </Button>
            <Button variant="outlined" color="secondary">
              Secondary Outlined
            </Button>
            <Button variant="outlined" color="primary" startIcon={<Settings />}>
              With Icon
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Text Buttons */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Text Buttons (variant="text")
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <Button variant="text" color="primary">
              Primary Text
            </Button>
            <Button variant="text" color="secondary">
              Secondary Text
            </Button>
            <Button variant="text" color="primary" startIcon={<Delete />}>
              Delete
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Button Groups */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Button Groups
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <ButtonGroup variant="contained" color="primary">
              <Button>One</Button>
              <Button>Two</Button>
              <Button>Three</Button>
            </ButtonGroup>
            <ButtonGroup variant="outlined" color="primary">
              <Button>Left</Button>
              <Button>Center</Button>
              <Button>Right</Button>
            </ButtonGroup>
          </Stack>
        </CardContent>
      </Card>

      {/* Theme Information */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current Theme Colors
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ p: 2, backgroundColor: theme.palette.primary.main, borderRadius: 1 }}>
                <Typography variant="body2" sx={{ color: 'white' }}>
                  Primary Main
                </Typography>
                <Typography variant="caption" sx={{ color: 'white' }}>
                  {theme.palette.primary.main}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ p: 2, backgroundColor: theme.palette.primary.light, borderRadius: 1 }}>
                <Typography variant="body2" sx={{ color: theme.palette.text.primary }}>
                  Primary Light
                </Typography>
                <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                  {theme.palette.primary.light}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ p: 2, backgroundColor: theme.palette.secondary.main, borderRadius: 1 }}>
                <Typography variant="body2" sx={{ color: 'white' }}>
                  Secondary Main
                </Typography>
                <Typography variant="caption" sx={{ color: 'white' }}>
                  {theme.palette.secondary.main}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ p: 2, backgroundColor: theme.palette.secondary.light, borderRadius: 1 }}>
                <Typography variant="body2" sx={{ color: theme.palette.text.primary }}>
                  Secondary Light
                </Typography>
                <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                  {theme.palette.secondary.light}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: theme.palette.grey[50], borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>
          How to Use Theme-Aware Buttons
        </Typography>
        <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
{`// ✅ Correct - Uses theme colors automatically
<Button variant="contained" color="primary">
  Primary Button
</Button>

// ✅ Correct - Uses theme with sx prop
<Button 
  variant="contained" 
  sx={{ 
    backgroundColor: theme.palette.primary.main,
    '&:hover': {
      backgroundColor: theme.palette.primary.dark
    }
  }}
>
  Custom Styled
</Button>

// ❌ Avoid - Hardcoded colors
<Button 
  variant="contained" 
  style={{ backgroundColor: '#3B30C8' }}
>
  Hardcoded Color
</Button>`}
        </Typography>
      </Box>
    </Box>
  );
};

export default ThemeButtonDemo;
