import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

const createProxy = (prefix, target, rewritePrefix = "/rest") => ({
  target,
  changeOrigin: true,
  rewrite: (path) => path.replace(new RegExp(`^${prefix}`), rewritePrefix),
});

// ✅ Your hardcoded tokens (keep short here for readability)
const IWA_TOKEN = `Bearer YOUR_FULL_IWA_TOKEN`;
const IDM_TOKEN = `Bearer ********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O_8jkIb2iufo0C6Y8QB6zWtNvIoXmyGoYvHdvVkto3mABRg10OJQlnaNx3CzYkVLKGDqmZaL1h024NBeB4vaRiJwYZnaRYKsHJafEx-fpL0PlaIErPBOShtQqMmRbXGBsogkWJ2C4R8EsrLTOtdnb7Gq4Tim66BLN7bWFd2Ur6aK4ajUKHE30IVe7LGsD8cxOPCFHvIvV7csfYe0GwAtqIyBcYhr-bt_x-XonEPwZhRVVb2_mAQXl0W5fp_Geen2QRtxNNItYwqHwP-Vu3SeIegCMgnBj-jmMjizHSPAWWi31DwUB6wgxbBM5KppLPVA_EvBwzG4KJt654x3h0Jlow`;

export default defineConfig({
  optimizeDeps: {
    include: ["@emotion/react", "@emotion/styled", "@mui/material/Tooltip"],
  },
  plugins: [
    react({
      jsxImportSource: "@emotion/react",
      babel: {
        plugins: ["@emotion/babel-plugin"],
      },
    }),
  ],
  resolve: {
    alias: {
      "@components": path.resolve(__dirname, "src/components"),
      "@constant": path.resolve(__dirname, "src/constant"),
      "@data": path.resolve(__dirname, "src/data"),
      "@helper": path.resolve(__dirname, "src/helper"),
      "@hooks": path.resolve(__dirname, "src/hooks"),
      "@utilityImages": path.resolve(__dirname, "src/utilityImages"),
      "@app": path.resolve(__dirname, "src/app"),
    },
  },
  server: {
    host: "0.0.0.0",
    port: 5173,
    open: true,
    proxy: {
      "/IWAApi": {
        target: "https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/IWAApi/, ""),
        secure: true,
        headers: {
          Authorization: IWA_TOKEN,
        },
      },
       "/cw-caf-idm-services": {
        ...createProxy(
          "/cw-caf-idm-services",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/",
          "/rest"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkUtilsServices": {
        ...createProxy(
          "/WorkUtilsServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRulesServices": {
        ...createProxy(
          "/WorkRulesServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRuleEngineServices": {
        ...createProxy(
          "/WorkRuleEngineServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
    },
  },
});
