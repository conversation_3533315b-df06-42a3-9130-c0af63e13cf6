import {
  Box,
  Grid,
  Typography,
  FormControl,
  MenuItem,
  Select,
  CircularProgress,
  Backdrop,
  Button
} from "@mui/material";
import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { doAjax } from "../Common/fetchService";
import { appSettingsUpdate } from "../../app/appSettingsSlice";
import { destination_Admin } from "../../destinationVariables";
import { font_Small } from "../Common/commonStyles";
import useLang from "../../hooks/useLang";
import ReusablePromptBox from "../Common/ReusablePromptBox/ReusablePromptBox";

export default function LanguageSettings() {
  const [loading, setLoading] = useState(false);
  const appSettings = useSelector((state) => state.appSettings);
  let userData = useSelector((state) => state.userManagement.userData);
  const dispatch = useDispatch();
  const { t } = useLang();

  const [SettingsObj, setSettingsObj] = useState({
    language: appSettings.language,
  });

  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);

  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });

  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      setPromptBoxScenario("");
      setFormValidationErrorItems([]);
    },
    handleOpenPromptBox: (ref, data = {}) => {
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
    },
    getCancelFunction: () => {
      return promptAction_Functions.handleClosePromptBox;
    },
    getCloseFunction: () => {
      return promptAction_Functions.handleClosePromptBox;
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  const handleSelect = (e) => {
    setSettingsObj({ ...SettingsObj, [e.target.name]: e.target.value });
  };

  const handleClear = () => {
    setSettingsObj({
      language: ""
    });
    setFormValidationErrorItems([]);
  };

  const handleSave = () => {
    let payload = {
      email: userData?.emailId,
      language: SettingsObj.language,
    };
    let hSuccess = (data) => {
      dispatch(appSettingsUpdate({ language: SettingsObj.language }));
      if (data.status === "Success") {
        promptAction_Functions.handleOpenPromptBox("SUCCESS", {
          message: `Language Settings Updated Successfully`,
          redirectOnClose: true,
        });
      } else {
        promptAction_Functions.handleOpenPromptBox("ERROR", {
          title: "Failed",
          message: `Language Settings Update Failed`,
          severity: "danger",
          cancelButton: false,
        });
      }
    };
    let hError = () => {
      promptAction_Functions.handleOpenPromptBox("ERROR", {
        title: "Failed",
        message: `Language Settings Update Failed`,
        severity: "danger",
        cancelButton: false,
      });
    };
    doAjax(
      `/${destination_Admin}/applicationSetting/saveApplicationSetting`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  return (
    <>
      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText}
        showCancelButton={promptBoxState.cancelButton}
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />

      <Backdrop sx={{ color: "#fff", zIndex: 100 }} open={loading}>
        <CircularProgress color="primary" />
      </Backdrop>

      <Box sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t("Language Settings")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
              <Select
                placeholder="Select Language"
                size="small"
                value={SettingsObj.language}
                name={"language"}
                onChange={handleSelect}
                displayEmpty
                sx={font_Small}
                error={formValidationErrorItems.includes("language")}
              >
                <MenuItem sx={font_Small} value={""}>
                  <div style={{ color: "#C1C1C1" }}>Select Language</div>
                </MenuItem>
                {[
                  "English",
                  "Hindi",
                  "Japanese",
                  "French",
                  "Spanish",
                  "Odia",
                  "German",
                  "Russian",
                  "Arabic",
                  "Greek",
                ].map((lang) => (
                  <MenuItem key={lang} value={lang}>{lang}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box display="flex" justifyContent="flex-end" mt={2} gap={1}>
          <Button
            variant="outlined"
            onClick={handleClear}
            sx={{ textTransform: "capitalize" }}
          >
            {t("Clear")}
          </Button>
          <Button
            variant="contained"
            onClick={handleSave}
            sx={{ textTransform: "capitalize" }}
          >
            {t("Save")}
          </Button>
        </Box>
      </Box>
    </>
  );
}
